import { executeNeo4jQuery, executeNeo4jTransaction } from '@/config/neo4j';
import { cacheGet, cacheSet, cacheDel } from '@/config/redis';
import { config } from '@/config/environment';
import { logger, logError } from '@/utils/logger';
import { NotFoundError, DatabaseError } from '@/middleware/errorHandler';
import { v4 as uuidv4 } from 'uuid';

interface UserProfile {
  id: string;
  userId: string;
  healthGoals: string[];
  currentSupplements: string[];
  allergies: string[];
  medicalConditions: string[];
  preferences: {
    budget: number;
    naturalOnly: boolean;
    veganOnly: boolean;
    glutenFree: boolean;
    categories: string[];
  };
  restrictions: {
    maxDailyPills: number;
    avoidIngredients: string[];
    preferredBrands: string[];
  };
  profileData: {
    age?: number;
    gender?: string;
    weight?: number;
    activityLevel?: string;
    dietType?: string;
  };
  createdAt: string;
  updatedAt: string;
}

interface SupplementRecommendation {
  supplementId: string;
  name: string;
  category: string;
  score: number;
  reasoning: string;
  benefits: string[];
  dosage: string;
  timing: string[];
  price: number;
  safetyRating: number;
  evidenceLevel: number;
  interactions: any[];
  alternatives: string[];
}

interface ProfileAnalytics {
  completeness: number;
  riskFactors: string[];
  recommendations: SupplementRecommendation[];
  stackOptimization: {
    currentCost: number;
    optimizedCost: number;
    savings: number;
    improvements: string[];
  };
  healthScore: number;
  goalProgress: Record<string, number>;
}

class UserProfileService {
  async createUserProfile(userId: string, profileData: Partial<UserProfile>): Promise<UserProfile> {
    try {
      const profileId = uuidv4();
      const now = new Date().toISOString();

      const profile: UserProfile = {
        id: profileId,
        userId,
        healthGoals: profileData.healthGoals || [],
        currentSupplements: profileData.currentSupplements || [],
        allergies: profileData.allergies || [],
        medicalConditions: profileData.medicalConditions || [],
        preferences: {
          budget: 100,
          naturalOnly: false,
          veganOnly: false,
          glutenFree: false,
          categories: [],
          ...profileData.preferences
        },
        restrictions: {
          maxDailyPills: 10,
          avoidIngredients: [],
          preferredBrands: [],
          ...profileData.restrictions
        },
        profileData: profileData.profileData || {},
        createdAt: now,
        updatedAt: now,
      };

      // Create user profile node in Neo4j
      const query = `
        CREATE (p:UserProfile $profile)
        RETURN p
      `;

      await executeNeo4jQuery(query, { profile });

      // Create relationships to current supplements
      if (profile.currentSupplements.length > 0) {
        await this.linkSupplementsToProfile(profileId, profile.currentSupplements);
      }

      // Cache the profile
      await cacheSet(`profile:${userId}`, profile, config.cache.ttl);

      logger.info(`Created user profile for user ${userId}`);
      return profile;
    } catch (error) {
      logError('Failed to create user profile', error, { userId });
      throw new DatabaseError('Failed to create user profile');
    }
  }

  async getUserProfile(userId: string): Promise<UserProfile | null> {
    try {
      // Check cache first
      const cached = await cacheGet(`profile:${userId}`);
      if (cached) return cached;

      const query = `
        MATCH (p:UserProfile {userId: $userId})
        RETURN p
      `;

      const result = await executeNeo4jQuery(query, { userId });

      if (result.records.length === 0) {
        return null;
      }

      const profileNode = result.records[0].get('p');
      const profile = profileNode.properties as UserProfile;

      // Get current supplements
      const supplementsQuery = `
        MATCH (p:UserProfile {userId: $userId})-[:TAKES]->(s:Supplement)
        RETURN s.id as supplementId, s.name as name
      `;

      const supplementsResult = await executeNeo4jQuery(supplementsQuery, { userId });
      profile.currentSupplements = supplementsResult.records.map((record: any) => 
        record.get('supplementId')
      );

      // Cache the profile
      await cacheSet(`profile:${userId}`, profile, config.cache.ttl);

      return profile;
    } catch (error) {
      logError('Failed to get user profile', error, { userId });
      throw new DatabaseError('Failed to retrieve user profile');
    }
  }

  async updateUserProfile(userId: string, updates: Partial<UserProfile>): Promise<UserProfile> {
    try {
      const existingProfile = await this.getUserProfile(userId);
      if (!existingProfile) {
        throw new NotFoundError(`Profile for user ${userId} not found`);
      }

      const updatedProfile = {
        ...existingProfile,
        ...updates,
        updatedAt: new Date().toISOString(),
      };

      const query = `
        MATCH (p:UserProfile {userId: $userId})
        SET p += $updates
        RETURN p
      `;

      await executeNeo4jQuery(query, { 
        userId, 
        updates: {
          ...updates,
          updatedAt: updatedProfile.updatedAt
        }
      });

      // Update supplement relationships if changed
      if (updates.currentSupplements) {
        await this.updateSupplementRelationships(userId, updates.currentSupplements);
      }

      // Invalidate cache
      await cacheDel(`profile:${userId}`);
      await cacheDel(`analytics:${userId}`);

      logger.info(`Updated user profile for user ${userId}`);
      return updatedProfile;
    } catch (error) {
      if (error instanceof NotFoundError) throw error;
      logError('Failed to update user profile', error, { userId });
      throw new DatabaseError('Failed to update user profile');
    }
  }

  async addSupplementToProfile(userId: string, supplementId: string, dosage?: string, timing?: string[]): Promise<void> {
    try {
      const profile = await this.getUserProfile(userId);
      if (!profile) {
        throw new NotFoundError(`Profile for user ${userId} not found`);
      }

      // Check if supplement already exists
      if (profile.currentSupplements.includes(supplementId)) {
        logger.warn(`Supplement ${supplementId} already in profile for user ${userId}`);
        return;
      }

      // Create relationship in Neo4j
      const query = `
        MATCH (p:UserProfile {userId: $userId}), (s:Supplement {id: $supplementId})
        CREATE (p)-[r:TAKES {
          dosage: $dosage,
          timing: $timing,
          addedAt: datetime(),
          active: true
        }]->(s)
        RETURN r
      `;

      await executeNeo4jQuery(query, {
        userId,
        supplementId,
        dosage: dosage || '',
        timing: timing || []
      });

      // Update profile cache
      profile.currentSupplements.push(supplementId);
      await cacheSet(`profile:${userId}`, profile, config.cache.ttl);

      // Invalidate analytics cache
      await cacheDel(`analytics:${userId}`);

      logger.info(`Added supplement ${supplementId} to profile for user ${userId}`);
    } catch (error) {
      logError('Failed to add supplement to profile', error, { userId, supplementId });
      throw new DatabaseError('Failed to add supplement to profile');
    }
  }

  async removeSupplementFromProfile(userId: string, supplementId: string): Promise<void> {
    try {
      const query = `
        MATCH (p:UserProfile {userId: $userId})-[r:TAKES]->(s:Supplement {id: $supplementId})
        DELETE r
        RETURN count(r) as deleted
      `;

      const result = await executeNeo4jQuery(query, { userId, supplementId });
      const deleted = result.records[0].get('deleted').toNumber();

      if (deleted === 0) {
        throw new NotFoundError(`Supplement ${supplementId} not found in profile for user ${userId}`);
      }

      // Update cache
      await cacheDel(`profile:${userId}`);
      await cacheDel(`analytics:${userId}`);

      logger.info(`Removed supplement ${supplementId} from profile for user ${userId}`);
    } catch (error) {
      if (error instanceof NotFoundError) throw error;
      logError('Failed to remove supplement from profile', error, { userId, supplementId });
      throw new DatabaseError('Failed to remove supplement from profile');
    }
  }

  async getPersonalizedRecommendations(userId: string, limit: number = 10): Promise<SupplementRecommendation[]> {
    try {
      const cacheKey = `recommendations:${userId}:${limit}`;
      const cached = await cacheGet(cacheKey);
      if (cached) return cached;

      const profile = await this.getUserProfile(userId);
      if (!profile) {
        throw new NotFoundError(`Profile for user ${userId} not found`);
      }

      // Complex Neo4j query for personalized recommendations
      const query = `
        MATCH (p:UserProfile {userId: $userId})
        OPTIONAL MATCH (p)-[:TAKES]->(current:Supplement)
        
        // Find supplements that synergize with current ones
        OPTIONAL MATCH (current)-[:SYNERGIZES_WITH]->(synergistic:Supplement)
        WHERE NOT (p)-[:TAKES]->(synergistic)
        
        // Find supplements for health goals
        OPTIONAL MATCH (goal:HealthGoal)<-[:TARGETS]-(goalSupplement:Supplement)
        WHERE goal.name IN $healthGoals
          AND NOT (p)-[:TAKES]->(goalSupplement)
        
        // Find popular supplements in preferred categories
        OPTIONAL MATCH (popular:Supplement)
        WHERE popular.category IN $preferredCategories
          AND popular.popularity > 70
          AND NOT (p)-[:TAKES]->(popular)
          AND popular.price <= $budget
        
        WITH DISTINCT 
          COALESCE(synergistic, goalSupplement, popular) as supplement,
          CASE 
            WHEN synergistic IS NOT NULL THEN 'synergistic'
            WHEN goalSupplement IS NOT NULL THEN 'goal-based'
            ELSE 'popular'
          END as reason
        
        WHERE supplement IS NOT NULL
        
        RETURN supplement, reason
        ORDER BY 
          CASE reason
            WHEN 'synergistic' THEN 3
            WHEN 'goal-based' THEN 2
            ELSE 1
          END DESC,
          supplement.popularity DESC,
          supplement.safetyRating DESC
        LIMIT $limit
      `;

      const result = await executeNeo4jQuery(query, {
        userId,
        healthGoals: profile.healthGoals,
        preferredCategories: profile.preferences.categories,
        budget: profile.preferences.budget,
        limit
      });

      const recommendations: SupplementRecommendation[] = result.records.map((record: any) => {
        const supplement = record.get('supplement');
        const reason = record.get('reason');
        
        return {
          supplementId: supplement.properties.id,
          name: supplement.properties.name,
          category: supplement.properties.category,
          score: this.calculateRecommendationScore(supplement.properties, reason, profile),
          reasoning: this.generateReasoningText(reason, supplement.properties, profile),
          benefits: supplement.properties.benefits || [],
          dosage: supplement.properties.recommendedDosage || '',
          timing: supplement.properties.timing || [],
          price: supplement.properties.price || 0,
          safetyRating: supplement.properties.safetyRating || 5,
          evidenceLevel: supplement.properties.evidenceLevel || 1,
          interactions: [],
          alternatives: [],
        };
      });

      // Cache recommendations for 1 hour
      await cacheSet(cacheKey, recommendations, 3600);

      return recommendations;
    } catch (error) {
      if (error instanceof NotFoundError) throw error;
      logError('Failed to get personalized recommendations', error, { userId });
      throw new DatabaseError('Failed to get personalized recommendations');
    }
  }

  private async linkSupplementsToProfile(profileId: string, supplementIds: string[]): Promise<void> {
    const queries = supplementIds.map(supplementId => ({
      query: `
        MATCH (p:UserProfile {id: $profileId}), (s:Supplement {id: $supplementId})
        CREATE (p)-[:TAKES {addedAt: datetime(), active: true}]->(s)
      `,
      parameters: { profileId, supplementId }
    }));

    await executeNeo4jTransaction(queries);
  }

  private async updateSupplementRelationships(userId: string, newSupplementIds: string[]): Promise<void> {
    // Remove all existing relationships
    const deleteQuery = `
      MATCH (p:UserProfile {userId: $userId})-[r:TAKES]->(:Supplement)
      DELETE r
    `;

    await executeNeo4jQuery(deleteQuery, { userId });

    // Add new relationships
    if (newSupplementIds.length > 0) {
      const queries = newSupplementIds.map(supplementId => ({
        query: `
          MATCH (p:UserProfile {userId: $userId}), (s:Supplement {id: $supplementId})
          CREATE (p)-[:TAKES {addedAt: datetime(), active: true}]->(s)
        `,
        parameters: { userId, supplementId }
      }));

      await executeNeo4jTransaction(queries);
    }
  }

  private calculateRecommendationScore(supplement: any, reason: string, profile: UserProfile): number {
    let score = 0;

    // Base score from supplement properties
    score += (supplement.popularity || 0) * 0.3;
    score += (supplement.safetyRating || 0) * 10 * 0.4;
    score += (supplement.evidenceLevel || 0) * 20 * 0.3;

    // Bonus for reason type
    switch (reason) {
      case 'synergistic':
        score += 30;
        break;
      case 'goal-based':
        score += 20;
        break;
      default:
        score += 5;
    }

    // Price consideration
    if (supplement.price <= profile.preferences.budget * 0.5) {
      score += 10;
    } else if (supplement.price > profile.preferences.budget) {
      score -= 20;
    }

    return Math.round(score);
  }

  private generateReasoningText(reason: string, supplement: any, profile: UserProfile): string {
    switch (reason) {
      case 'synergistic':
        return `This supplement works synergistically with your current stack, potentially enhancing the benefits of supplements you're already taking.`;
      case 'goal-based':
        return `This supplement aligns with your health goals: ${profile.healthGoals.join(', ')}. It may help you achieve better results in these areas.`;
      default:
        return `This is a popular, well-researched supplement in the ${supplement.category} category that fits within your preferences and budget.`;
    }
  }
}

export default UserProfileService;
