import express from 'express';
import { body, param, query, validationResult } from 'express-validator';
import UserProfileService from '@/services/UserProfileService';
import { logger } from '@/utils/logger';
import { NotFoundError, ValidationError } from '@/middleware/errorHandler';

const router = express.Router();
const userProfileService = new UserProfileService();

// Validation middleware
const validateRequest = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }
  next();
};

// GET /api/profile/:userId - Get user profile
router.get('/:userId',
  param('userId').isString().notEmpty(),
  validateRequest,
  async (req: express.Request, res: express.Response, next: express.NextFunction) => {
    try {
      const { userId } = req.params;
      const profile = await userProfileService.getUserProfile(userId);
      
      if (!profile) {
        throw new NotFoundError(`Profile for user ${userId} not found`);
      }

      res.json({
        success: true,
        data: profile
      });
    } catch (error) {
      next(error);
    }
  }
);

// POST /api/profile/:userId - Create or update user profile
router.post('/:userId',
  param('userId').isString().notEmpty(),
  body('healthGoals').optional().isArray(),
  body('currentSupplements').optional().isArray(),
  body('allergies').optional().isArray(),
  body('medicalConditions').optional().isArray(),
  body('preferences').optional().isObject(),
  body('restrictions').optional().isObject(),
  body('profileData').optional().isObject(),
  validateRequest,
  async (req: express.Request, res: express.Response, next: express.NextFunction) => {
    try {
      const { userId } = req.params;
      const profileData = req.body;

      // Check if profile exists
      const existingProfile = await userProfileService.getUserProfile(userId);
      
      let profile;
      if (existingProfile) {
        profile = await userProfileService.updateUserProfile(userId, profileData);
      } else {
        profile = await userProfileService.createUserProfile(userId, profileData);
      }

      res.json({
        success: true,
        data: profile,
        message: existingProfile ? 'Profile updated successfully' : 'Profile created successfully'
      });
    } catch (error) {
      next(error);
    }
  }
);

// PUT /api/profile/:userId - Update user profile
router.put('/:userId',
  param('userId').isString().notEmpty(),
  body('healthGoals').optional().isArray(),
  body('currentSupplements').optional().isArray(),
  body('allergies').optional().isArray(),
  body('medicalConditions').optional().isArray(),
  body('preferences').optional().isObject(),
  body('restrictions').optional().isObject(),
  body('profileData').optional().isObject(),
  validateRequest,
  async (req: express.Request, res: express.Response, next: express.NextFunction) => {
    try {
      const { userId } = req.params;
      const updates = req.body;

      const profile = await userProfileService.updateUserProfile(userId, updates);

      res.json({
        success: true,
        data: profile,
        message: 'Profile updated successfully'
      });
    } catch (error) {
      next(error);
    }
  }
);

// POST /api/profile/:userId/supplements - Add supplement to profile
router.post('/:userId/supplements',
  param('userId').isString().notEmpty(),
  body('supplementId').isString().notEmpty(),
  body('dosage').optional().isString(),
  body('timing').optional().isArray(),
  validateRequest,
  async (req: express.Request, res: express.Response, next: express.NextFunction) => {
    try {
      const { userId } = req.params;
      const { supplementId, dosage, timing } = req.body;

      await userProfileService.addSupplementToProfile(userId, supplementId, dosage, timing);

      res.json({
        success: true,
        message: 'Supplement added to profile successfully'
      });
    } catch (error) {
      next(error);
    }
  }
);

// DELETE /api/profile/:userId/supplements/:supplementId - Remove supplement from profile
router.delete('/:userId/supplements/:supplementId',
  param('userId').isString().notEmpty(),
  param('supplementId').isString().notEmpty(),
  validateRequest,
  async (req: express.Request, res: express.Response, next: express.NextFunction) => {
    try {
      const { userId, supplementId } = req.params;

      await userProfileService.removeSupplementFromProfile(userId, supplementId);

      res.json({
        success: true,
        message: 'Supplement removed from profile successfully'
      });
    } catch (error) {
      next(error);
    }
  }
);

// GET /api/profile/:userId/recommendations - Get personalized recommendations
router.get('/:userId/recommendations',
  param('userId').isString().notEmpty(),
  query('limit').optional().isInt({ min: 1, max: 50 }),
  validateRequest,
  async (req: express.Request, res: express.Response, next: express.NextFunction) => {
    try {
      const { userId } = req.params;
      const limit = parseInt(req.query.limit as string) || 10;

      const recommendations = await userProfileService.getPersonalizedRecommendations(userId, limit);

      res.json({
        success: true,
        data: recommendations,
        count: recommendations.length
      });
    } catch (error) {
      next(error);
    }
  }
);

// POST /api/profile/:userId/link-supplements - Bulk add supplements from external link/profile
router.post('/:userId/link-supplements',
  param('userId').isString().notEmpty(),
  body('source').isString().notEmpty(), // 'manual', 'import', 'recommendation'
  body('supplements').isArray().notEmpty(),
  body('supplements.*.id').isString().notEmpty(),
  body('supplements.*.dosage').optional().isString(),
  body('supplements.*.timing').optional().isArray(),
  body('supplements.*.notes').optional().isString(),
  validateRequest,
  async (req: express.Request, res: express.Response, next: express.NextFunction) => {
    try {
      const { userId } = req.params;
      const { source, supplements } = req.body;

      const results = {
        added: 0,
        skipped: 0,
        errors: [] as string[]
      };

      // Process each supplement
      for (const supplement of supplements) {
        try {
          await userProfileService.addSupplementToProfile(
            userId, 
            supplement.id, 
            supplement.dosage, 
            supplement.timing
          );
          results.added++;
        } catch (error) {
          if (error instanceof Error && error.message.includes('already in profile')) {
            results.skipped++;
          } else {
            results.errors.push(`Failed to add ${supplement.id}: ${error instanceof Error ? error.message : 'Unknown error'}`);
          }
        }
      }

      logger.info(`Bulk supplement linking completed for user ${userId}`, {
        source,
        results
      });

      res.json({
        success: true,
        data: results,
        message: `Successfully processed ${supplements.length} supplements. Added: ${results.added}, Skipped: ${results.skipped}, Errors: ${results.errors.length}`
      });
    } catch (error) {
      next(error);
    }
  }
);

// GET /api/profile/:userId/analytics - Get profile analytics
router.get('/:userId/analytics',
  param('userId').isString().notEmpty(),
  validateRequest,
  async (req: express.Request, res: express.Response, next: express.NextFunction) => {
    try {
      const { userId } = req.params;
      
      // This would be implemented in UserProfileService
      // For now, return basic analytics
      const profile = await userProfileService.getUserProfile(userId);
      if (!profile) {
        throw new NotFoundError(`Profile for user ${userId} not found`);
      }

      const analytics = {
        completeness: calculateProfileCompleteness(profile),
        supplementCount: profile.currentSupplements.length,
        goalsCovered: profile.healthGoals.length,
        estimatedMonthlyCost: 0, // Would calculate from current supplements
        riskFactors: [], // Would analyze interactions and contraindications
        recommendations: await userProfileService.getPersonalizedRecommendations(userId, 5)
      };

      res.json({
        success: true,
        data: analytics
      });
    } catch (error) {
      next(error);
    }
  }
);

// Helper function to calculate profile completeness
function calculateProfileCompleteness(profile: any): number {
  let score = 0;
  const maxScore = 100;

  // Basic info (30 points)
  if (profile.profileData.age) score += 5;
  if (profile.profileData.gender) score += 5;
  if (profile.profileData.weight) score += 5;
  if (profile.profileData.activityLevel) score += 5;
  if (profile.profileData.dietType) score += 5;
  if (profile.preferences.budget > 0) score += 5;

  // Health goals (20 points)
  if (profile.healthGoals.length > 0) score += 10;
  if (profile.healthGoals.length >= 3) score += 10;

  // Current supplements (20 points)
  if (profile.currentSupplements.length > 0) score += 10;
  if (profile.currentSupplements.length >= 3) score += 10;

  // Allergies and conditions (15 points)
  if (profile.allergies.length >= 0) score += 5; // Even empty array shows consideration
  if (profile.medicalConditions.length >= 0) score += 5;
  if (profile.restrictions.avoidIngredients.length >= 0) score += 5;

  // Preferences (15 points)
  if (profile.preferences.categories.length > 0) score += 5;
  if (profile.preferences.naturalOnly !== undefined) score += 5;
  if (profile.restrictions.maxDailyPills > 0) score += 5;

  return Math.min(score, maxScore);
}

export default router;
