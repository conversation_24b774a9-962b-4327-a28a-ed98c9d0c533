import React, { useRef, useEffect, useState, useCallback } from 'react';
import * as d3 from 'd3';
import { motion } from 'framer-motion';
import {
  MagnifyingGlassIcon,
  AdjustmentsHorizontalIcon,
  ArrowsPointingOutIcon,
  FunnelIcon,
} from '@heroicons/react/24/outline';

interface GraphNode {
  id: string;
  name: string;
  type: 'supplement' | 'ingredient' | 'effect' | 'condition' | 'interaction';
  category?: string;
  size: number;
  color: string;
  x?: number;
  y?: number;
  fx?: number | null;
  fy?: number | null;
  metadata?: {
    safetyRating?: number;
    evidenceLevel?: number;
    popularity?: number;
    description?: string;
  };
}

interface GraphLink {
  source: string | GraphNode;
  target: string | GraphNode;
  type: 'contains' | 'causes' | 'interacts' | 'treats' | 'contraindicated';
  strength: number;
  color: string;
  metadata?: {
    evidenceLevel?: number;
    severity?: string;
    mechanism?: string;
  };
}

interface GraphData {
  nodes: GraphNode[];
  links: GraphLink[];
}

interface CosmicKnowledgeGraphProps {
  data: GraphData;
  width?: number;
  height?: number;
  onNodeClick?: (node: GraphNode) => void;
  onNodeHover?: (node: GraphNode | null) => void;
}

const CosmicKnowledgeGraph: React.FC<CosmicKnowledgeGraphProps> = ({
  data,
  width = 1200,
  height = 800,
  onNodeClick,
  onNodeHover,
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTypes, setSelectedTypes] = useState<string[]>(['supplement', 'ingredient', 'effect']);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [selectedNode, setSelectedNode] = useState<GraphNode | null>(null);
  const [simulation, setSimulation] = useState<d3.Simulation<GraphNode, GraphLink> | null>(null);

  // Golden ratio for cosmic spacing
  const φ = 1.618;

  // Color scheme based on cosmic design system
  const nodeColors = {
    supplement: '#3b82f6',    // Primary blue
    ingredient: '#22c55e',    // Secondary green
    effect: '#f59e0b',        // Warning orange
    condition: '#ef4444',     // Error red
    interaction: '#8b5cf6',   // Purple
  };

  const linkColors = {
    contains: '#94a3b8',      // Gray
    causes: '#22c55e',        // Green
    interacts: '#f59e0b',     // Orange
    treats: '#3b82f6',        // Blue
    contraindicated: '#ef4444', // Red
  };

  // Filter data based on search and type selection
  console.log('CosmicKnowledgeGraph: data prop value:', data);
  const filteredData = React.useMemo(() => {
    if (!data || !data.nodes) {
      console.warn('CosmicKnowledgeGraph: data or data.nodes is undefined/null. Returning empty graph data.');
      return { nodes: [], links: [] };
    }

    const filteredNodes = data.nodes.filter(node => {
      const matchesSearch = searchTerm === '' ||
        node.name.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesType = selectedTypes.includes(node.type);
      return matchesSearch && matchesType;
    });

    const nodeIds = new Set(filteredNodes.map(n => n.id));
    const filteredLinks = data.links.filter(link => {
      const sourceId = typeof link.source === 'string' ? link.source : link.source.id;
      const targetId = typeof link.target === 'string' ? link.target : link.target.id;
      return nodeIds.has(sourceId) && nodeIds.has(targetId);
    });

    return { nodes: filteredNodes, links: filteredLinks };
  }, [data, searchTerm, selectedTypes]);

  // Initialize D3 force simulation
  useEffect(() => {
    if (!svgRef.current || filteredData.nodes.length === 0) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    // Create zoom behavior
    const zoom = d3.zoom<SVGSVGElement, unknown>()
      .scaleExtent([0.1, 4])
      .on('zoom', (event) => {
        const { transform } = event;
        setZoomLevel(transform.k);
        svg.select('.graph-container').attr('transform', transform);
      });

    svg.call(zoom);

    // Create container for graph elements
    const container = svg.append('g').attr('class', 'graph-container');

    // Create force simulation with cosmic-inspired forces
    const newSimulation = d3.forceSimulation<GraphNode>(filteredData.nodes)
      .force('link', d3.forceLink<GraphNode, GraphLink>(filteredData.links)
        .id(d => d.id)
        .distance(d => 50 + (d.strength * 100))
        .strength(d => d.strength * 0.5)
      )
      .force('charge', d3.forceManyBody()
        .strength(d => -300 - ((d as GraphNode).size * 10))
      )
      .force('center', d3.forceCenter(width / 2, height / 2))
      .force('collision', d3.forceCollide()
        .radius(d => (d as GraphNode).size + 5)
      )
      .force('x', d3.forceX(width / 2).strength(0.1))
      .force('y', d3.forceY(height / 2).strength(0.1));

    // Create links
    const links = container.selectAll('.link')
      .data(filteredData.links)
      .enter()
      .append('line')
      .attr('class', 'link')
      .attr('stroke', d => d.color)
      .attr('stroke-width', d => Math.sqrt(d.strength * 5))
      .attr('stroke-opacity', 0.6)
      .style('cursor', 'pointer');

    // Create nodes
    const nodes = container.selectAll('.node')
      .data(filteredData.nodes)
      .enter()
      .append('g')
      .attr('class', 'node')
      .style('cursor', 'pointer');

    // Add circles for nodes
    nodes.append('circle')
      .attr('r', d => d.size)
      .attr('fill', d => d.color)
      .attr('stroke', '#fff')
      .attr('stroke-width', 2)
      .style('filter', 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))')
      .on('mouseover', function(event, d) {
        d3.select(this)
          .transition()
          .duration(161) // φ * 100
          .attr('r', d.size * φ)
          .style('filter', 'drop-shadow(0 4px 8px rgba(0,0,0,0.2))');
        
        onNodeHover?.(d);
      })
      .on('mouseout', function(event, d) {
        d3.select(this)
          .transition()
          .duration(161)
          .attr('r', d.size)
          .style('filter', 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))');
        
        onNodeHover?.(null);
      })
      .on('click', function(event, d) {
        setSelectedNode(d);
        onNodeClick?.(d);
      });

    // Add labels for nodes
    nodes.append('text')
      .text(d => d.name)
      .attr('text-anchor', 'middle')
      .attr('dy', d => d.size + 15)
      .attr('font-size', '12px')
      .attr('font-weight', '500')
      .attr('fill', '#374151')
      .style('pointer-events', 'none')
      .style('user-select', 'none');

    // Add drag behavior
    const drag = d3.drag<SVGGElement, GraphNode>()
      .on('start', (event, d) => {
        if (!event.active) newSimulation.alphaTarget(0.3).restart();
        d.fx = d.x;
        d.fy = d.y;
      })
      .on('drag', (event, d) => {
        d.fx = event.x;
        d.fy = event.y;
      })
      .on('end', (event, d) => {
        if (!event.active) newSimulation.alphaTarget(0);
        d.fx = null;
        d.fy = null;
      });

    nodes.call(drag);

    // Update positions on simulation tick
    newSimulation.on('tick', () => {
      links
        .attr('x1', d => (d.source as GraphNode).x!)
        .attr('y1', d => (d.source as GraphNode).y!)
        .attr('x2', d => (d.target as GraphNode).x!)
        .attr('y2', d => (d.target as GraphNode).y!);

      nodes.attr('transform', d => `translate(${d.x},${d.y})`);
    });

    setSimulation(newSimulation);

    return () => {
      newSimulation.stop();
    };
  }, [filteredData, width, height, onNodeClick, onNodeHover]);

  const handleTypeToggle = (type: string) => {
    setSelectedTypes(prev => 
      prev.includes(type) 
        ? prev.filter(t => t !== type)
        : [...prev, type]
    );
  };

  const handleZoomIn = () => {
    if (svgRef.current) {
      const svg = d3.select(svgRef.current);
      svg.transition().duration(300).call(
        d3.zoom<SVGSVGElement, unknown>().scaleBy as any,
        φ
      );
    }
  };

  const handleZoomOut = () => {
    if (svgRef.current) {
      const svg = d3.select(svgRef.current);
      svg.transition().duration(300).call(
        d3.zoom<SVGSVGElement, unknown>().scaleBy as any,
        1 / φ
      );
    }
  };

  const handleReset = () => {
    if (svgRef.current) {
      const svg = d3.select(svgRef.current);
      svg.transition().duration(500).call(
        d3.zoom<SVGSVGElement, unknown>().transform as any,
        d3.zoomIdentity
      );
    }
    simulation?.alpha(1).restart();
  };

  return (
    <div className="cosmic-card p-0 overflow-hidden">
      {/* Controls */}
      <div className="p-4 border-b border-gray-200 bg-gray-50">
        <div className="flex flex-wrap items-center justify-between gap-4">
          {/* Search */}
          <div className="relative flex-1 min-w-64">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search supplements, ingredients, effects..."
              className="cosmic-input pl-10"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          {/* Type Filters */}
          <div className="flex items-center space-x-2">
            <FunnelIcon className="w-5 h-5 text-gray-500" />
            {Object.keys(nodeColors).map(type => (
              <button
                key={type}
                onClick={() => handleTypeToggle(type)}
                className={`px-3 py-1 rounded-full text-xs font-medium transition-all ${
                  selectedTypes.includes(type)
                    ? 'bg-primary-100 text-primary-700 border border-primary-300'
                    : 'bg-gray-100 text-gray-600 border border-gray-300'
                }`}
              >
                <span 
                  className="inline-block w-2 h-2 rounded-full mr-2"
                  style={{ backgroundColor: nodeColors[type as keyof typeof nodeColors] }}
                ></span>
                {type}
              </button>
            ))}
          </div>

          {/* Zoom Controls */}
          <div className="flex items-center space-x-2">
            <button
              onClick={handleZoomIn}
              className="cosmic-button cosmic-button--outline p-2"
              title="Zoom In"
            >
              +
            </button>
            <span className="text-sm text-gray-600 min-w-16 text-center">
              {Math.round(zoomLevel * 100)}%
            </span>
            <button
              onClick={handleZoomOut}
              className="cosmic-button cosmic-button--outline p-2"
              title="Zoom Out"
            >
              -
            </button>
            <button
              onClick={handleReset}
              className="cosmic-button cosmic-button--outline p-2"
              title="Reset View"
            >
              <ArrowsPointingOutIcon className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Graph Container */}
      <div className="relative">
        <svg
          ref={svgRef}
          width={width}
          height={height}
          className="w-full h-full"
          style={{ background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)' }}
        />

        {/* Node Info Panel */}
        {selectedNode && (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
            className="absolute top-4 right-4 cosmic-card w-80 max-h-96 overflow-y-auto"
          >
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-lg font-semibold text-gray-900">{selectedNode.name}</h3>
              <button
                onClick={() => setSelectedNode(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                ×
              </button>
            </div>
            
            <div className="space-y-3">
              <div>
                <span className="cosmic-badge cosmic-badge--primary">
                  {selectedNode.type}
                </span>
                {selectedNode.category && (
                  <span className="cosmic-badge cosmic-badge--success ml-2">
                    {selectedNode.category}
                  </span>
                )}
              </div>

              {selectedNode.metadata?.description && (
                <p className="text-sm text-gray-600">
                  {selectedNode.metadata.description}
                </p>
              )}

              {selectedNode.metadata && (
                <div className="grid grid-cols-2 gap-3 text-sm">
                  {selectedNode.metadata.safetyRating && (
                    <div>
                      <span className="text-gray-500">Safety:</span>
                      <span className="ml-1 font-medium">
                        {selectedNode.metadata.safetyRating}/10
                      </span>
                    </div>
                  )}
                  {selectedNode.metadata.evidenceLevel && (
                    <div>
                      <span className="text-gray-500">Evidence:</span>
                      <span className="ml-1 font-medium">
                        {selectedNode.metadata.evidenceLevel}/5
                      </span>
                    </div>
                  )}
                  {selectedNode.metadata.popularity && (
                    <div>
                      <span className="text-gray-500">Popularity:</span>
                      <span className="ml-1 font-medium">
                        {selectedNode.metadata.popularity}%
                      </span>
                    </div>
                  )}
                </div>
              )}
            </div>
          </motion.div>
        )}

        {/* Stats */}
        <div className="absolute bottom-4 left-4 cosmic-card bg-white/90 backdrop-blur">
          <div className="text-sm text-gray-600">
            <div>Nodes: {filteredData.nodes.length}</div>
            <div>Connections: {filteredData.links.length}</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CosmicKnowledgeGraph;
