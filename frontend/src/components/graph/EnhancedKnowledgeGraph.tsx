import React, { useEffect, useRef, useState, useCallback } from 'react';
import * as d3 from 'd3';
import { motion, AnimatePresence } from 'framer-motion';
import {
  MagnifyingGlassIcon,
  AdjustmentsHorizontalIcon,
  ArrowsPointingOutIcon,
  EyeIcon,
  EyeSlashIcon,
  PlayIcon,
  PauseIcon,
  UserIcon,
} from '@heroicons/react/24/outline';

interface GraphNode {
  id: string;
  name: string;
  type: string;
  category?: string;
  properties: Record<string, any>;
  x?: number;
  y?: number;
  fx?: number | null;
  fy?: number | null;
  vx?: number;
  vy?: number;
}

interface GraphLink {
  id: string;
  source: string | GraphNode;
  target: string | GraphNode;
  type: string;
  strength?: number;
  properties: Record<string, any>;
}

interface GraphData {
  nodes: GraphNode[];
  links: GraphLink[];
}

interface EnhancedKnowledgeGraphProps {
  userId?: string;
  data?: GraphData;
  width?: number;
  height?: number;
  onNodeClick?: (node: GraphNode) => void;
  onNodeHover?: (node: GraphNode | null) => void;
  showControls?: boolean;
  enableUserProfile?: boolean;
}

const EnhancedKnowledgeGraph: React.FC<EnhancedKnowledgeGraphProps> = ({
  userId,
  data,
  width = 800,
  height = 600,
  onNodeClick,
  onNodeHover,
  showControls = true,
  enableUserProfile = false,
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const [graphData, setGraphData] = useState<GraphData>({ nodes: [], links: [] });
  const [selectedNode, setSelectedNode] = useState<GraphNode | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredTypes, setFilteredTypes] = useState<string[]>([]);
  const [isSimulationRunning, setIsSimulationRunning] = useState(true);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [userProfile, setUserProfile] = useState<any>(null);
  const [highlightedNodes, setHighlightedNodes] = useState<Set<string>>(new Set());

  // D3 simulation and zoom refs
  const simulationRef = useRef<d3.Simulation<GraphNode, GraphLink> | null>(null);
  const zoomRef = useRef<d3.ZoomBehavior<SVGSVGElement, unknown> | null>(null);

  // Load user profile if enabled
  useEffect(() => {
    if (enableUserProfile && userId) {
      loadUserProfile();
    }
  }, [enableUserProfile, userId]);

  const loadUserProfile = async () => {
    try {
      const response = await fetch(`/api/profile/${userId}`);
      if (response.ok) {
        const data = await response.json();
        setUserProfile(data.data);
        
        // Highlight user's current supplements
        if (data.data.currentSupplements) {
          setHighlightedNodes(new Set(data.data.currentSupplements));
        }
      }
    } catch (error) {
      console.error('Failed to load user profile:', error);
    }
  };

  // Load graph data
  useEffect(() => {
    if (data) {
      setGraphData(data);
    } else {
      loadGraphData();
    }
  }, [data]);

  const loadGraphData = async () => {
    try {
      const response = await fetch('/api/graph?limit=100');
      if (response.ok) {
        const result = await response.json();
        setGraphData(result.data);
      }
    } catch (error) {
      console.error('Failed to load graph data:', error);
    }
  };

  // Initialize D3 visualization
  useEffect(() => {
    if (!svgRef.current || !graphData.nodes.length) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    // Create main group for zoom/pan
    const g = svg.append('g').attr('class', 'graph-container');

    // Initialize zoom behavior
    const zoom = d3.zoom<SVGSVGElement, unknown>()
      .scaleExtent([0.1, 4])
      .on('zoom', (event) => {
        g.attr('transform', event.transform);
        setZoomLevel(event.transform.k);
      });

    svg.call(zoom);
    zoomRef.current = zoom;

    // Filter data based on search and type filters
    const filteredNodes = graphData.nodes.filter(node => {
      const matchesSearch = !searchTerm || 
        node.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        node.type.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesType = filteredTypes.length === 0 || filteredTypes.includes(node.type);
      
      return matchesSearch && matchesType;
    });

    const filteredNodeIds = new Set(filteredNodes.map(n => n.id));
    const filteredLinks = graphData.links.filter(link => {
      const sourceId = typeof link.source === 'string' ? link.source : link.source.id;
      const targetId = typeof link.target === 'string' ? link.target : link.target.id;
      return filteredNodeIds.has(sourceId) && filteredNodeIds.has(targetId);
    });

    // Create force simulation
    const simulation = d3.forceSimulation<GraphNode>(filteredNodes)
      .force('link', d3.forceLink<GraphNode, GraphLink>(filteredLinks)
        .id(d => d.id)
        .distance(d => 50 + (d.strength || 1) * 30)
        .strength(d => (d.strength || 0.5) * 0.8)
      )
      .force('charge', d3.forceManyBody()
        .strength(d => {
          const baseStrength = -300;
          const typeMultiplier = d.type === 'Supplement' ? 1.5 : 1;
          const highlightMultiplier = highlightedNodes.has(d.id) ? 1.3 : 1;
          return baseStrength * typeMultiplier * highlightMultiplier;
        })
      )
      .force('center', d3.forceCenter(width / 2, height / 2))
      .force('collision', d3.forceCollide().radius(d => {
        const baseRadius = 20;
        const typeMultiplier = d.type === 'Supplement' ? 1.2 : 1;
        const highlightMultiplier = highlightedNodes.has(d.id) ? 1.3 : 1;
        return baseRadius * typeMultiplier * highlightMultiplier;
      }));

    simulationRef.current = simulation;

    // Create links
    const link = g.append('g')
      .attr('class', 'links')
      .selectAll('line')
      .data(filteredLinks)
      .enter().append('line')
      .attr('class', 'graph-link')
      .attr('stroke', d => getLinkColor(d.type))
      .attr('stroke-width', d => Math.sqrt((d.strength || 0.5) * 4))
      .attr('stroke-opacity', 0.6);

    // Create nodes
    const node = g.append('g')
      .attr('class', 'nodes')
      .selectAll('g')
      .data(filteredNodes)
      .enter().append('g')
      .attr('class', 'graph-node')
      .call(d3.drag<SVGGElement, GraphNode>()
        .on('start', dragstarted)
        .on('drag', dragged)
        .on('end', dragended)
      );

    // Add circles to nodes
    node.append('circle')
      .attr('r', d => {
        const baseRadius = 8;
        const typeMultiplier = d.type === 'Supplement' ? 1.5 : 1;
        const highlightMultiplier = highlightedNodes.has(d.id) ? 1.4 : 1;
        return baseRadius * typeMultiplier * highlightMultiplier;
      })
      .attr('fill', d => getNodeColor(d.type, highlightedNodes.has(d.id)))
      .attr('stroke', d => highlightedNodes.has(d.id) ? '#fbbf24' : '#fff')
      .attr('stroke-width', d => highlightedNodes.has(d.id) ? 3 : 1.5);

    // Add labels to nodes
    node.append('text')
      .text(d => d.name)
      .attr('x', 12)
      .attr('y', 4)
      .attr('class', 'graph-label')
      .style('font-size', d => highlightedNodes.has(d.id) ? '12px' : '10px')
      .style('font-weight', d => highlightedNodes.has(d.id) ? 'bold' : 'normal')
      .style('fill', '#374151');

    // Add node interactions
    node
      .on('click', (event, d) => {
        setSelectedNode(d);
        onNodeClick?.(d);
      })
      .on('mouseenter', (event, d) => {
        onNodeHover?.(d);
        // Highlight connected nodes
        const connectedNodeIds = new Set<string>();
        filteredLinks.forEach(link => {
          const sourceId = typeof link.source === 'string' ? link.source : link.source.id;
          const targetId = typeof link.target === 'string' ? link.target : link.target.id;
          
          if (sourceId === d.id) connectedNodeIds.add(targetId);
          if (targetId === d.id) connectedNodeIds.add(sourceId);
        });

        node.style('opacity', n => connectedNodeIds.has(n.id) || n.id === d.id ? 1 : 0.3);
        link.style('opacity', l => {
          const sourceId = typeof l.source === 'string' ? l.source : l.source.id;
          const targetId = typeof l.target === 'string' ? l.target : l.target.id;
          return sourceId === d.id || targetId === d.id ? 1 : 0.1;
        });
      })
      .on('mouseleave', () => {
        onNodeHover?.(null);
        node.style('opacity', 1);
        link.style('opacity', 0.6);
      });

    // Update positions on simulation tick
    simulation.on('tick', () => {
      link
        .attr('x1', d => (d.source as GraphNode).x!)
        .attr('y1', d => (d.source as GraphNode).y!)
        .attr('x2', d => (d.target as GraphNode).x!)
        .attr('y2', d => (d.target as GraphNode).y!);

      node.attr('transform', d => `translate(${d.x},${d.y})`);
    });

    // Drag functions
    function dragstarted(event: any, d: GraphNode) {
      if (!event.active) simulation.alphaTarget(0.3).restart();
      d.fx = d.x;
      d.fy = d.y;
    }

    function dragged(event: any, d: GraphNode) {
      d.fx = event.x;
      d.fy = event.y;
    }

    function dragended(event: any, d: GraphNode) {
      if (!event.active) simulation.alphaTarget(0);
      d.fx = null;
      d.fy = null;
    }

    // Control simulation state
    if (!isSimulationRunning) {
      simulation.stop();
    }

    return () => {
      simulation.stop();
    };
  }, [graphData, searchTerm, filteredTypes, isSimulationRunning, highlightedNodes]);

  // Helper functions
  const getNodeColor = (type: string, isHighlighted: boolean) => {
    const colors: Record<string, string> = {
      'Supplement': isHighlighted ? '#f59e0b' : '#3b82f6',
      'Ingredient': '#10b981',
      'Effect': '#8b5cf6',
      'Study': '#f97316',
      'Condition': '#ef4444',
      'Brand': '#6b7280',
    };
    return colors[type] || '#9ca3af';
  };

  const getLinkColor = (type: string) => {
    const colors: Record<string, string> = {
      'CONTAINS': '#10b981',
      'HAS_EFFECT': '#3b82f6',
      'SYNERGIZES_WITH': '#22c55e',
      'ANTAGONIZES_WITH': '#ef4444',
      'STUDIED_IN': '#f97316',
      'TREATS': '#8b5cf6',
    };
    return colors[type] || '#9ca3af';
  };

  const handleZoomIn = () => {
    if (zoomRef.current && svgRef.current) {
      d3.select(svgRef.current).transition().call(
        zoomRef.current.scaleBy, 1.5
      );
    }
  };

  const handleZoomOut = () => {
    if (zoomRef.current && svgRef.current) {
      d3.select(svgRef.current).transition().call(
        zoomRef.current.scaleBy, 1 / 1.5
      );
    }
  };

  const handleResetZoom = () => {
    if (zoomRef.current && svgRef.current) {
      d3.select(svgRef.current).transition().call(
        zoomRef.current.transform,
        d3.zoomIdentity
      );
    }
  };

  const toggleSimulation = () => {
    setIsSimulationRunning(!isSimulationRunning);
    if (simulationRef.current) {
      if (isSimulationRunning) {
        simulationRef.current.stop();
      } else {
        simulationRef.current.restart();
      }
    }
  };

  const addSupplementToProfile = async (supplementId: string) => {
    if (!userId) return;
    
    try {
      const response = await fetch(`/api/profile/${userId}/supplements`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ supplementId }),
      });

      if (response.ok) {
        // Update highlighted nodes
        setHighlightedNodes(prev => new Set([...prev, supplementId]));
        await loadUserProfile(); // Refresh profile data
      }
    } catch (error) {
      console.error('Failed to add supplement to profile:', error);
    }
  };

  // Get unique node types for filtering
  const nodeTypes = Array.from(new Set(graphData.nodes.map(n => n.type)));

  return (
    <div className="relative w-full h-full">
      {/* Controls */}
      {showControls && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="absolute top-4 left-4 z-10 space-y-2"
        >
          {/* Search */}
          <div className="cosmic-card p-3">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search nodes..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="cosmic-input pl-10 pr-4 py-2 w-64"
              />
            </div>
          </div>

          {/* Type Filters */}
          <div className="cosmic-card p-3">
            <h4 className="text-sm font-medium text-gray-700 mb-2">Node Types</h4>
            <div className="flex flex-wrap gap-1">
              {nodeTypes.map(type => (
                <button
                  key={type}
                  onClick={() => {
                    setFilteredTypes(prev => 
                      prev.includes(type) 
                        ? prev.filter(t => t !== type)
                        : [...prev, type]
                    );
                  }}
                  className={`cosmic-badge text-xs ${
                    filteredTypes.length === 0 || filteredTypes.includes(type)
                      ? 'cosmic-badge--primary'
                      : 'cosmic-badge--outline'
                  }`}
                >
                  {type}
                </button>
              ))}
            </div>
          </div>
        </motion.div>
      )}

      {/* Zoom Controls */}
      {showControls && (
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          className="absolute top-4 right-4 z-10 space-y-2"
        >
          <div className="cosmic-card p-2 flex flex-col space-y-1">
            <button
              onClick={handleZoomIn}
              className="cosmic-button cosmic-button--outline p-2"
              title="Zoom In"
            >
              <ArrowsPointingOutIcon className="w-4 h-4" />
            </button>
            <button
              onClick={handleZoomOut}
              className="cosmic-button cosmic-button--outline p-2"
              title="Zoom Out"
            >
              <AdjustmentsHorizontalIcon className="w-4 h-4" />
            </button>
            <button
              onClick={handleResetZoom}
              className="cosmic-button cosmic-button--outline p-2"
              title="Reset Zoom"
            >
              <EyeIcon className="w-4 h-4" />
            </button>
            <button
              onClick={toggleSimulation}
              className="cosmic-button cosmic-button--outline p-2"
              title={isSimulationRunning ? "Pause Simulation" : "Start Simulation"}
            >
              {isSimulationRunning ? (
                <PauseIcon className="w-4 h-4" />
              ) : (
                <PlayIcon className="w-4 h-4" />
              )}
            </button>
          </div>
          
          <div className="cosmic-card p-2 text-xs text-gray-600">
            Zoom: {Math.round(zoomLevel * 100)}%
          </div>
        </motion.div>
      )}

      {/* Main Graph */}
      <svg
        ref={svgRef}
        width={width}
        height={height}
        className="w-full h-full border border-gray-200 rounded-lg bg-white"
      />

      {/* Node Details Panel */}
      <AnimatePresence>
        {selectedNode && (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
            className="absolute bottom-4 right-4 z-10 cosmic-card p-4 w-80"
          >
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-semibold text-gray-900">{selectedNode.name}</h3>
              <button
                onClick={() => setSelectedNode(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                ×
              </button>
            </div>
            
            <div className="space-y-2 text-sm">
              <div>
                <span className="text-gray-500">Type:</span>
                <span className="ml-2 cosmic-badge cosmic-badge--primary text-xs">
                  {selectedNode.type}
                </span>
              </div>
              
              {selectedNode.category && (
                <div>
                  <span className="text-gray-500">Category:</span>
                  <span className="ml-2 font-medium">{selectedNode.category}</span>
                </div>
              )}

              {Object.entries(selectedNode.properties).slice(0, 3).map(([key, value]) => (
                <div key={key}>
                  <span className="text-gray-500 capitalize">{key}:</span>
                  <span className="ml-2 font-medium">{String(value)}</span>
                </div>
              ))}
            </div>

            {enableUserProfile && selectedNode.type === 'Supplement' && (
              <div className="mt-4 pt-3 border-t border-gray-200">
                <button
                  onClick={() => addSupplementToProfile(selectedNode.id)}
                  disabled={highlightedNodes.has(selectedNode.id)}
                  className={`cosmic-button w-full ${
                    highlightedNodes.has(selectedNode.id)
                      ? 'cosmic-button--outline opacity-50 cursor-not-allowed'
                      : 'cosmic-button--primary'
                  }`}
                >
                  <UserIcon className="w-4 h-4 mr-2" />
                  {highlightedNodes.has(selectedNode.id) ? 'In Your Profile' : 'Add to Profile'}
                </button>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {/* User Profile Info */}
      {enableUserProfile && userProfile && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="absolute bottom-4 left-4 z-10 cosmic-card p-3"
        >
          <div className="flex items-center space-x-2 text-sm">
            <UserIcon className="w-4 h-4 text-primary-500" />
            <span className="text-gray-600">
              {userProfile.currentSupplements?.length || 0} supplements in profile
            </span>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default EnhancedKnowledgeGraph;
