import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  SparklesIcon,
  BeakerIcon,
  ShieldCheckIcon,
  StarIcon,
  HeartIcon,
  BoltIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';
import { debounce } from 'lodash';

interface Supplement {
  id: string;
  name: string;
  category: string;
  description: string;
  safetyRating: number;
  evidenceLevel: number;
  popularity: number;
  price: number;
  effects: string[];
  interactions: number;
  benefits: string[];
  warnings: string[];
  dosage: string;
  brand: string;
  image?: string;
}

interface SearchFilters {
  categories: string[];
  safetyRating: number;
  evidenceLevel: number;
  priceRange: [number, number];
  effects: string[];
  sortBy: 'relevance' | 'popularity' | 'safety' | 'price' | 'evidence';
}

interface CosmicSupplementSearchProps {
  userId?: string;
  onSupplementSelect?: (supplement: Supplement) => void;
  onAddToStack?: (supplement: Supplement) => void;
  onAddToProfile?: (supplement: Supplement) => void;
  showProfileIntegration?: boolean;
}

const CosmicSupplementSearch: React.FC<CosmicSupplementSearchProps> = ({
  userId,
  onSupplementSelect,
  onAddToStack,
  onAddToProfile,
  showProfileIntegration = false,
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [supplements, setSupplements] = useState<Supplement[]>([]);
  const [filteredSupplements, setFilteredSupplements] = useState<Supplement[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedSupplement, setSelectedSupplement] = useState<Supplement | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [userProfile, setUserProfile] = useState<any>(null);
  const [personalizedResults, setPersonalizedResults] = useState<boolean>(false);
  const [filters, setFilters] = useState<SearchFilters>({
    categories: [],
    safetyRating: 0,
    evidenceLevel: 0,
    priceRange: [0, 200],
    effects: [],
    sortBy: 'relevance',
  });

  // Sample supplement data - replace with API calls
  const sampleSupplements: Supplement[] = [
    {
      id: 'vitamin-d3',
      name: 'Vitamin D3 (Cholecalciferol)',
      category: 'Vitamins',
      description: 'Essential vitamin for bone health, immune function, and mood regulation.',
      safetyRating: 9,
      evidenceLevel: 5,
      popularity: 95,
      price: 15,
      effects: ['Bone Health', 'Immune Support', 'Mood Enhancement'],
      interactions: 2,
      benefits: ['Supports calcium absorption', 'Boosts immune system', 'May improve mood'],
      warnings: ['High doses may cause hypercalcemia'],
      dosage: '1000-4000 IU daily',
      brand: 'Nordic Naturals',
    },
    {
      id: 'magnesium-glycinate',
      name: 'Magnesium Glycinate',
      category: 'Minerals',
      description: 'Highly bioavailable form of magnesium for muscle relaxation and sleep.',
      safetyRating: 8,
      evidenceLevel: 4,
      popularity: 87,
      price: 25,
      effects: ['Sleep Quality', 'Muscle Relaxation', 'Stress Reduction'],
      interactions: 3,
      benefits: ['Improves sleep quality', 'Reduces muscle cramps', 'Supports nervous system'],
      warnings: ['May cause digestive upset in high doses'],
      dosage: '200-400mg before bed',
      brand: 'Thorne',
    },
    {
      id: 'omega-3',
      name: 'Omega-3 EPA/DHA',
      category: 'Fatty Acids',
      description: 'Essential fatty acids for brain health and inflammation reduction.',
      safetyRating: 9,
      evidenceLevel: 5,
      popularity: 92,
      price: 35,
      effects: ['Brain Health', 'Heart Health', 'Anti-inflammatory'],
      interactions: 1,
      benefits: ['Supports cognitive function', 'Reduces inflammation', 'Heart protective'],
      warnings: ['May increase bleeding risk with blood thinners'],
      dosage: '1-2g EPA/DHA daily',
      brand: 'Nordic Naturals',
    },
    {
      id: 'ashwagandha',
      name: 'Ashwagandha Root Extract',
      category: 'Adaptogens',
      description: 'Adaptogenic herb for stress reduction and cortisol management.',
      safetyRating: 7,
      evidenceLevel: 4,
      popularity: 78,
      price: 28,
      effects: ['Stress Reduction', 'Energy Enhancement', 'Sleep Support'],
      interactions: 4,
      benefits: ['Reduces cortisol levels', 'Improves stress resilience', 'May boost energy'],
      warnings: ['May interact with thyroid medications', 'Avoid during pregnancy'],
      dosage: '300-600mg daily',
      brand: 'KSM-66',
    },
    {
      id: 'curcumin',
      name: 'Curcumin with Piperine',
      category: 'Herbs',
      description: 'Powerful anti-inflammatory compound from turmeric with enhanced absorption.',
      safetyRating: 8,
      evidenceLevel: 4,
      popularity: 82,
      price: 32,
      effects: ['Anti-inflammatory', 'Joint Health', 'Antioxidant'],
      interactions: 2,
      benefits: ['Reduces inflammation', 'Supports joint health', 'Powerful antioxidant'],
      warnings: ['May increase bleeding risk', 'Avoid with gallstones'],
      dosage: '500-1000mg daily with meals',
      brand: 'Meriva',
    },
  ];

  useEffect(() => {
    setSupplements(sampleSupplements);
    setFilteredSupplements(sampleSupplements);

    // Load user profile if userId provided
    if (userId && showProfileIntegration) {
      loadUserProfile();
    }
  }, [userId, showProfileIntegration]);

  const loadUserProfile = async () => {
    try {
      const response = await fetch(`/api/profile/${userId}`);
      if (response.ok) {
        const data = await response.json();
        setUserProfile(data.data);
      }
    } catch (error) {
      console.error('Failed to load user profile:', error);
    }
  };

  // Debounced search function
  const debouncedSearch = useMemo(
    () => debounce((query: string) => {
      performSearch(query);
    }, 300),
    [supplements, filters]
  );

  useEffect(() => {
    debouncedSearch(searchQuery);
    return () => {
      debouncedSearch.cancel();
    };
  }, [searchQuery, debouncedSearch]);

  const performSearch = useCallback((query: string) => {
    setLoading(true);
    
    let filtered = supplements;

    // Text search
    if (query.trim()) {
      filtered = filtered.filter(supplement =>
        supplement.name.toLowerCase().includes(query.toLowerCase()) ||
        supplement.description.toLowerCase().includes(query.toLowerCase()) ||
        supplement.effects.some(effect => 
          effect.toLowerCase().includes(query.toLowerCase())
        ) ||
        supplement.category.toLowerCase().includes(query.toLowerCase())
      );
    }

    // Apply filters
    if (filters.categories.length > 0) {
      filtered = filtered.filter(supplement =>
        filters.categories.includes(supplement.category)
      );
    }

    if (filters.safetyRating > 0) {
      filtered = filtered.filter(supplement =>
        supplement.safetyRating >= filters.safetyRating
      );
    }

    if (filters.evidenceLevel > 0) {
      filtered = filtered.filter(supplement =>
        supplement.evidenceLevel >= filters.evidenceLevel
      );
    }

    filtered = filtered.filter(supplement =>
      supplement.price >= filters.priceRange[0] &&
      supplement.price <= filters.priceRange[1]
    );

    if (filters.effects.length > 0) {
      filtered = filtered.filter(supplement =>
        filters.effects.some(effect =>
          supplement.effects.includes(effect)
        )
      );
    }

    // Sort results
    switch (filters.sortBy) {
      case 'popularity':
        filtered.sort((a, b) => b.popularity - a.popularity);
        break;
      case 'safety':
        filtered.sort((a, b) => b.safetyRating - a.safetyRating);
        break;
      case 'price':
        filtered.sort((a, b) => a.price - b.price);
        break;
      case 'evidence':
        filtered.sort((a, b) => b.evidenceLevel - a.evidenceLevel);
        break;
      default:
        // Relevance sorting (keep original order for now)
        break;
    }

    setFilteredSupplements(filtered);
    setLoading(false);
  }, [supplements, filters]);

  const handleSupplementClick = (supplement: Supplement) => {
    setSelectedSupplement(supplement);
    onSupplementSelect?.(supplement);
  };

  const handleAddToStack = (supplement: Supplement) => {
    onAddToStack?.(supplement);
  };

  const handleAddToProfile = async (supplement: Supplement) => {
    if (!userId) return;

    try {
      const response = await fetch(`/api/profile/${userId}/supplements`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          supplementId: supplement.id,
          dosage: supplement.dosage,
          timing: ['morning'] // Default timing
        }),
      });

      if (response.ok) {
        onAddToProfile?.(supplement);
        // Reload user profile to update current supplements
        await loadUserProfile();
      }
    } catch (error) {
      console.error('Failed to add supplement to profile:', error);
    }
  };

  const getPersonalizedScore = (supplement: Supplement): number => {
    if (!userProfile || !personalizedResults) return 0;

    let score = 0;

    // Check if supplement aligns with health goals
    if (userProfile.healthGoals.some((goal: string) =>
      supplement.effects.some(effect => effect.toLowerCase().includes(goal.toLowerCase()))
    )) {
      score += 30;
    }

    // Check if supplement is within budget
    if (supplement.price <= userProfile.preferences.budget) {
      score += 20;
    }

    // Check if supplement matches preferences
    if (userProfile.preferences.categories.includes(supplement.category)) {
      score += 15;
    }

    // Check if not already taking
    if (!userProfile.currentSupplements.includes(supplement.id)) {
      score += 10;
    } else {
      score -= 50; // Heavily penalize if already taking
    }

    return score;
  };

  const getSafetyColor = (rating: number) => {
    if (rating >= 8) return 'text-secondary-600';
    if (rating >= 6) return 'text-warning-600';
    return 'text-error-600';
  };

  const getEvidenceColor = (level: number) => {
    if (level >= 4) return 'text-secondary-600';
    if (level >= 3) return 'text-warning-600';
    return 'text-gray-600';
  };

  const categories = ['Vitamins', 'Minerals', 'Herbs', 'Adaptogens', 'Fatty Acids', 'Probiotics'];
  const effects = ['Bone Health', 'Immune Support', 'Sleep Quality', 'Stress Reduction', 'Brain Health', 'Heart Health', 'Anti-inflammatory', 'Energy Enhancement'];

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Search Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="cosmic-card mb-6"
      >
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-3xl font-bold cosmic-gradient bg-clip-text text-transparent">
              🔍 Cosmic Supplement Search
            </h1>
            <p className="text-gray-600 mt-2">
              Discover the perfect supplements for your health journey
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <div className="cosmic-badge cosmic-badge--primary">
              {filteredSupplements.length} Results
            </div>
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`cosmic-button ${showFilters ? 'cosmic-button--primary' : 'cosmic-button--outline'}`}
            >
              <FunnelIcon className="w-4 h-4 mr-2" />
              Filters
            </button>
          </div>
        </div>

        {/* Search Bar */}
        <div className="relative">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            type="text"
            placeholder="Search supplements, effects, or categories..."
            className="cosmic-input pl-10 text-lg"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          {loading && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <div className="cosmic-spin w-5 h-5 border-2 border-primary-200 border-t-primary-600 rounded-full"></div>
            </div>
          )}
        </div>
      </motion.div>

      {/* Advanced Filters */}
      <AnimatePresence>
        {showFilters && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="cosmic-card mb-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Advanced Filters</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
              {/* Categories */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Categories</label>
                <div className="space-y-2 max-h-32 overflow-y-auto">
                  {categories.map(category => (
                    <label key={category} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={filters.categories.includes(category)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setFilters(prev => ({
                              ...prev,
                              categories: [...prev.categories, category]
                            }));
                          } else {
                            setFilters(prev => ({
                              ...prev,
                              categories: prev.categories.filter(c => c !== category)
                            }));
                          }
                        }}
                        className="mr-2"
                      />
                      <span className="text-sm">{category}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Safety Rating */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Min Safety Rating: {filters.safetyRating}/10
                </label>
                <input
                  type="range"
                  min="0"
                  max="10"
                  value={filters.safetyRating}
                  onChange={(e) => setFilters(prev => ({
                    ...prev,
                    safetyRating: parseInt(e.target.value)
                  }))}
                  className="w-full"
                />
              </div>

              {/* Evidence Level */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Min Evidence Level: {filters.evidenceLevel}/5
                </label>
                <input
                  type="range"
                  min="0"
                  max="5"
                  value={filters.evidenceLevel}
                  onChange={(e) => setFilters(prev => ({
                    ...prev,
                    evidenceLevel: parseInt(e.target.value)
                  }))}
                  className="w-full"
                />
              </div>

              {/* Sort By */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Sort By</label>
                <select
                  value={filters.sortBy}
                  onChange={(e) => setFilters(prev => ({
                    ...prev,
                    sortBy: e.target.value as any
                  }))}
                  className="cosmic-input"
                >
                  <option value="relevance">Relevance</option>
                  <option value="popularity">Popularity</option>
                  <option value="safety">Safety Rating</option>
                  <option value="evidence">Evidence Level</option>
                  <option value="price">Price (Low to High)</option>
                </select>
              </div>
            </div>

            {/* Effects Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Effects</label>
              <div className="flex flex-wrap gap-2">
                {effects.map(effect => (
                  <button
                    key={effect}
                    onClick={() => {
                      if (filters.effects.includes(effect)) {
                        setFilters(prev => ({
                          ...prev,
                          effects: prev.effects.filter(e => e !== effect)
                        }));
                      } else {
                        setFilters(prev => ({
                          ...prev,
                          effects: [...prev.effects, effect]
                        }));
                      }
                    }}
                    className={`px-3 py-1 rounded-full text-sm font-medium transition-all ${
                      filters.effects.includes(effect)
                        ? 'bg-primary-100 text-primary-700 border border-primary-300'
                        : 'bg-gray-100 text-gray-600 border border-gray-300 hover:bg-gray-200'
                    }`}
                  >
                    {effect}
                  </button>
                ))}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Results Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <AnimatePresence>
          {filteredSupplements.map((supplement, index) => (
            <motion.div
              key={supplement.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ delay: index * 0.1 }}
              className="cosmic-card cosmic-card--primary cursor-pointer hover:scale-105 transition-transform"
              onClick={() => handleSupplementClick(supplement)}
            >
              {/* Supplement Header */}
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 mb-1">{supplement.name}</h3>
                  <span className="cosmic-badge cosmic-badge--primary text-xs">
                    {supplement.category}
                  </span>
                </div>
                <div className="text-right">
                  <div className="text-lg font-bold text-primary-600">${supplement.price}</div>
                  <div className="text-xs text-gray-500">{supplement.brand}</div>
                </div>
              </div>

              {/* Description */}
              <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                {supplement.description}
              </p>

              {/* Metrics */}
              <div className="grid grid-cols-3 gap-2 mb-3">
                <div className="text-center">
                  <div className={`text-lg font-bold ${getSafetyColor(supplement.safetyRating)}`}>
                    {supplement.safetyRating}/10
                  </div>
                  <div className="text-xs text-gray-500">Safety</div>
                </div>
                <div className="text-center">
                  <div className={`text-lg font-bold ${getEvidenceColor(supplement.evidenceLevel)}`}>
                    {supplement.evidenceLevel}/5
                  </div>
                  <div className="text-xs text-gray-500">Evidence</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-warning-600">
                    {supplement.popularity}%
                  </div>
                  <div className="text-xs text-gray-500">Popular</div>
                </div>
              </div>

              {/* Effects */}
              <div className="mb-3">
                <div className="flex flex-wrap gap-1">
                  {supplement.effects.slice(0, 3).map(effect => (
                    <span
                      key={effect}
                      className="cosmic-badge cosmic-badge--success text-xs"
                    >
                      {effect}
                    </span>
                  ))}
                  {supplement.effects.length > 3 && (
                    <span className="cosmic-badge cosmic-badge--primary text-xs">
                      +{supplement.effects.length - 3} more
                    </span>
                  )}
                </div>
              </div>

              {/* Warnings */}
              {supplement.interactions > 0 && (
                <div className="flex items-center text-warning-600 text-xs mb-3">
                  <ExclamationTriangleIcon className="w-4 h-4 mr-1" />
                  {supplement.interactions} known interaction{supplement.interactions > 1 ? 's' : ''}
                </div>
              )}

              {/* Actions */}
              <div className="flex space-x-2">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleAddToStack(supplement);
                  }}
                  className="cosmic-button cosmic-button--primary flex-1"
                >
                  <BeakerIcon className="w-4 h-4 mr-2" />
                  Add to Stack
                </button>
                <button className="cosmic-button cosmic-button--outline">
                  <HeartIcon className="w-4 h-4" />
                </button>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* No Results */}
      {filteredSupplements.length === 0 && !loading && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center py-12"
        >
          <SparklesIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">No supplements found</h3>
          <p className="text-gray-600 mb-4">
            Try adjusting your search terms or filters to find what you're looking for.
          </p>
          <button
            onClick={() => {
              setSearchQuery('');
              setFilters({
                categories: [],
                safetyRating: 0,
                evidenceLevel: 0,
                priceRange: [0, 200],
                effects: [],
                sortBy: 'relevance',
              });
            }}
            className="cosmic-button cosmic-button--primary"
          >
            Clear All Filters
          </button>
        </motion.div>
      )}
    </div>
  );
};

export default CosmicSupplementSearch;
