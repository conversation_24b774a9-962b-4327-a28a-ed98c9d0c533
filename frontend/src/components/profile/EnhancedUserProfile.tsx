import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  UserIcon,
  HeartIcon,
  BeakerIcon,
  ShieldCheckIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  PlusIcon,
  LinkIcon,
  SparklesIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';

interface UserProfile {
  id: string;
  userId: string;
  healthGoals: string[];
  currentSupplements: string[];
  allergies: string[];
  medicalConditions: string[];
  preferences: {
    budget: number;
    naturalOnly: boolean;
    veganOnly: boolean;
    glutenFree: boolean;
    categories: string[];
  };
  restrictions: {
    maxDailyPills: number;
    avoidIngredients: string[];
    preferredBrands: string[];
  };
  profileData: {
    age?: number;
    gender?: string;
    weight?: number;
    activityLevel?: string;
    dietType?: string;
  };
}

interface SupplementRecommendation {
  supplementId: string;
  name: string;
  category: string;
  score: number;
  reasoning: string;
  benefits: string[];
  dosage: string;
  timing: string[];
  price: number;
  safetyRating: number;
  evidenceLevel: number;
}

interface EnhancedUserProfileProps {
  userId: string;
  onSupplementAdd?: (supplementId: string) => void;
  onProfileUpdate?: (profile: UserProfile) => void;
}

const EnhancedUserProfile: React.FC<EnhancedUserProfileProps> = ({
  userId,
  onSupplementAdd,
  onProfileUpdate,
}) => {
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [recommendations, setRecommendations] = useState<SupplementRecommendation[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [showLinkModal, setShowLinkModal] = useState(false);
  const [linkInput, setLinkInput] = useState('');
  const [analytics, setAnalytics] = useState<any>(null);

  useEffect(() => {
    loadUserProfile();
    loadRecommendations();
    loadAnalytics();
  }, [userId]);

  const loadUserProfile = async () => {
    try {
      const response = await fetch(`/api/profile/${userId}`);
      if (response.ok) {
        const data = await response.json();
        setProfile(data.data);
      } else if (response.status === 404) {
        // Create default profile
        await createDefaultProfile();
      }
    } catch (error) {
      console.error('Failed to load user profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const createDefaultProfile = async () => {
    try {
      const defaultProfile = {
        healthGoals: [],
        currentSupplements: [],
        allergies: [],
        medicalConditions: [],
        preferences: {
          budget: 100,
          naturalOnly: false,
          veganOnly: false,
          glutenFree: false,
          categories: [],
        },
        restrictions: {
          maxDailyPills: 10,
          avoidIngredients: [],
          preferredBrands: [],
        },
        profileData: {},
      };

      const response = await fetch(`/api/profile/${userId}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(defaultProfile),
      });

      if (response.ok) {
        const data = await response.json();
        setProfile(data.data);
      }
    } catch (error) {
      console.error('Failed to create default profile:', error);
    }
  };

  const loadRecommendations = async () => {
    try {
      const response = await fetch(`/api/profile/${userId}/recommendations?limit=10`);
      if (response.ok) {
        const data = await response.json();
        setRecommendations(data.data);
      }
    } catch (error) {
      console.error('Failed to load recommendations:', error);
    }
  };

  const loadAnalytics = async () => {
    try {
      const response = await fetch(`/api/profile/${userId}/analytics`);
      if (response.ok) {
        const data = await response.json();
        setAnalytics(data.data);
      }
    } catch (error) {
      console.error('Failed to load analytics:', error);
    }
  };

  const addSupplementToProfile = async (supplementId: string, dosage?: string, timing?: string[]) => {
    try {
      const response = await fetch(`/api/profile/${userId}/supplements`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ supplementId, dosage, timing }),
      });

      if (response.ok) {
        await loadUserProfile();
        await loadRecommendations();
        await loadAnalytics();
        onSupplementAdd?.(supplementId);
      }
    } catch (error) {
      console.error('Failed to add supplement:', error);
    }
  };

  const removeSupplementFromProfile = async (supplementId: string) => {
    try {
      const response = await fetch(`/api/profile/${userId}/supplements/${supplementId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        await loadUserProfile();
        await loadRecommendations();
        await loadAnalytics();
      }
    } catch (error) {
      console.error('Failed to remove supplement:', error);
    }
  };

  const handleLinkSupplements = async () => {
    if (!linkInput.trim()) return;

    try {
      // Parse the link input - could be a URL, list of supplement names, etc.
      const supplements = parseLinkInput(linkInput);
      
      const response = await fetch(`/api/profile/${userId}/link-supplements`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          source: 'manual',
          supplements,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        console.log('Link results:', data.data);
        await loadUserProfile();
        await loadRecommendations();
        setShowLinkModal(false);
        setLinkInput('');
      }
    } catch (error) {
      console.error('Failed to link supplements:', error);
    }
  };

  const parseLinkInput = (input: string) => {
    // Simple parsing - split by lines or commas
    const lines = input.split(/[\n,]/).map(line => line.trim()).filter(Boolean);
    
    return lines.map(line => ({
      id: line.toLowerCase().replace(/\s+/g, '-'), // Simple ID generation
      dosage: '', // Could be parsed from input
      timing: [], // Could be parsed from input
    }));
  };

  const updateProfile = async (updates: Partial<UserProfile>) => {
    try {
      const response = await fetch(`/api/profile/${userId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updates),
      });

      if (response.ok) {
        const data = await response.json();
        setProfile(data.data);
        onProfileUpdate?.(data.data);
        await loadRecommendations();
        await loadAnalytics();
      }
    } catch (error) {
      console.error('Failed to update profile:', error);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="cosmic-spin w-8 h-8 border-2 border-primary-200 border-t-primary-600 rounded-full"></div>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="text-center py-12">
        <UserIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-gray-900 mb-2">No Profile Found</h3>
        <p className="text-gray-600">Unable to load or create user profile.</p>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="cosmic-card mb-6"
      >
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold cosmic-gradient bg-clip-text text-transparent">
              👤 Your Health Profile
            </h1>
            <p className="text-gray-600 mt-2">
              Personalized supplement recommendations based on your health goals
            </p>
          </div>
          <div className="flex items-center space-x-4">
            {analytics && (
              <div className="cosmic-badge cosmic-badge--primary">
                {analytics.completeness}% Complete
              </div>
            )}
            <button
              onClick={() => setShowLinkModal(true)}
              className="cosmic-button cosmic-button--outline"
            >
              <LinkIcon className="w-4 h-4 mr-2" />
              Link Supplements
            </button>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="flex space-x-1 mt-6">
          {['overview', 'supplements', 'recommendations', 'settings'].map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              className={`px-4 py-2 rounded-lg font-medium transition-all ${
                activeTab === tab
                  ? 'bg-primary-100 text-primary-700 border border-primary-300'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              }`}
            >
              {tab.charAt(0).toUpperCase() + tab.slice(1)}
            </button>
          ))}
        </div>
      </motion.div>

      {/* Tab Content */}
      <AnimatePresence mode="wait">
        {activeTab === 'overview' && (
          <motion.div
            key="overview"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="grid grid-cols-1 lg:grid-cols-3 gap-6"
          >
            {/* Profile Stats */}
            <div className="lg:col-span-2 space-y-6">
              {/* Health Goals */}
              <div className="cosmic-card">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <HeartIcon className="w-5 h-5 mr-2 text-red-500" />
                  Health Goals
                </h3>
                <div className="flex flex-wrap gap-2">
                  {profile.healthGoals.map((goal, index) => (
                    <span key={index} className="cosmic-badge cosmic-badge--success">
                      {goal}
                    </span>
                  ))}
                  {profile.healthGoals.length === 0 && (
                    <p className="text-gray-500 italic">No health goals set</p>
                  )}
                </div>
              </div>

              {/* Current Supplements */}
              <div className="cosmic-card">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <BeakerIcon className="w-5 h-5 mr-2 text-blue-500" />
                  Current Supplements ({profile.currentSupplements.length})
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {profile.currentSupplements.map((supplementId, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <span className="font-medium">{supplementId}</span>
                      <button
                        onClick={() => removeSupplementFromProfile(supplementId)}
                        className="text-red-500 hover:text-red-700"
                      >
                        ×
                      </button>
                    </div>
                  ))}
                  {profile.currentSupplements.length === 0 && (
                    <p className="text-gray-500 italic col-span-2">No supplements added yet</p>
                  )}
                </div>
              </div>
            </div>

            {/* Analytics Sidebar */}
            <div className="space-y-6">
              {analytics && (
                <>
                  {/* Profile Completeness */}
                  <div className="cosmic-card">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Profile Completeness</h3>
                    <div className="cosmic-progress mb-2">
                      <div 
                        className="cosmic-progress__bar"
                        style={{ width: `${analytics.completeness}%` }}
                      />
                    </div>
                    <p className="text-sm text-gray-600">{analytics.completeness}% complete</p>
                  </div>

                  {/* Quick Stats */}
                  <div className="cosmic-card">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Stats</h3>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Supplements</span>
                        <span className="font-medium">{analytics.supplementCount}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Goals</span>
                        <span className="font-medium">{analytics.goalsCovered}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Monthly Cost</span>
                        <span className="font-medium">${analytics.estimatedMonthlyCost}</span>
                      </div>
                    </div>
                  </div>
                </>
              )}
            </div>
          </motion.div>
        )}

        {activeTab === 'recommendations' && (
          <motion.div
            key="recommendations"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="space-y-6"
          >
            <div className="cosmic-card">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <SparklesIcon className="w-5 h-5 mr-2 text-yellow-500" />
                Personalized Recommendations
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {recommendations.map((rec) => (
                  <div key={rec.supplementId} className="cosmic-card cosmic-card--primary">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h4 className="font-semibold text-gray-900">{rec.name}</h4>
                        <span className="cosmic-badge cosmic-badge--primary text-xs">
                          {rec.category}
                        </span>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold text-primary-600">
                          {rec.score}
                        </div>
                        <div className="text-xs text-gray-500">Score</div>
                      </div>
                    </div>

                    <p className="text-sm text-gray-600 mb-3">{rec.reasoning}</p>

                    <div className="grid grid-cols-2 gap-2 mb-3 text-sm">
                      <div>
                        <span className="text-gray-500">Safety:</span>
                        <span className="ml-1 font-medium">{rec.safetyRating}/10</span>
                      </div>
                      <div>
                        <span className="text-gray-500">Evidence:</span>
                        <span className="ml-1 font-medium">{rec.evidenceLevel}/5</span>
                      </div>
                      <div>
                        <span className="text-gray-500">Price:</span>
                        <span className="ml-1 font-medium">${rec.price}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">Dosage:</span>
                        <span className="ml-1 font-medium">{rec.dosage}</span>
                      </div>
                    </div>

                    <button
                      onClick={() => addSupplementToProfile(rec.supplementId, rec.dosage, rec.timing)}
                      className="cosmic-button cosmic-button--primary w-full"
                    >
                      <PlusIcon className="w-4 h-4 mr-2" />
                      Add to Profile
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Link Supplements Modal */}
      <AnimatePresence>
        {showLinkModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
            onClick={() => setShowLinkModal(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="cosmic-card max-w-md w-full mx-4"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Link Supplements</h3>
              <p className="text-gray-600 mb-4">
                Enter supplement names (one per line) or paste a list to add them to your profile:
              </p>
              <textarea
                value={linkInput}
                onChange={(e) => setLinkInput(e.target.value)}
                placeholder="Vitamin D3&#10;Magnesium Glycinate&#10;Omega-3 Fish Oil"
                className="cosmic-input h-32 resize-none mb-4"
              />
              <div className="flex space-x-3">
                <button
                  onClick={handleLinkSupplements}
                  className="cosmic-button cosmic-button--primary flex-1"
                >
                  Add Supplements
                </button>
                <button
                  onClick={() => setShowLinkModal(false)}
                  className="cosmic-button cosmic-button--outline"
                >
                  Cancel
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default EnhancedUserProfile;
