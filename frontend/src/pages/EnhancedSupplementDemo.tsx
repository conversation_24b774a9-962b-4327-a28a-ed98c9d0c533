import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  UserIcon,
  BeakerIcon,
  ChartBarIcon,
  CogIcon,
  SparklesIcon,
  LinkIcon,
} from '@heroicons/react/24/outline';

// Import our enhanced components
import EnhancedUserProfile from '@/components/profile/EnhancedUserProfile';
import CosmicSupplementSearch from '@/components/search/CosmicSupplementSearch';
import CosmicStackBuilder from '@/components/stack/CosmicStackBuilder';
import EnhancedKnowledgeGraph from '@/components/graph/EnhancedKnowledgeGraph';

interface DemoUser {
  id: string;
  name: string;
  email: string;
}

const EnhancedSupplementDemo: React.FC = () => {
  const [activeTab, setActiveTab] = useState('profile');
  const [demoUser] = useState<DemoUser>({
    id: 'demo-user-123',
    name: '<PERSON>',
    email: '<EMAIL>',
  });
  const [selectedSupplement, setSelectedSupplement] = useState<any>(null);
  const [stackSupplements, setStackSupplements] = useState<any[]>([]);

  const tabs = [
    { id: 'profile', name: 'User Profile', icon: UserIcon, description: 'Manage health goals and supplement preferences' },
    { id: 'search', name: 'AI Search', icon: BeakerIcon, description: 'Intelligent supplement discovery with personalization' },
    { id: 'stack', name: 'Stack Builder', icon: CogIcon, description: 'Build and optimize supplement stacks with AI' },
    { id: 'graph', name: 'Knowledge Graph', icon: ChartBarIcon, description: 'Interactive visualization of supplement relationships' },
  ];

  const handleSupplementSelect = (supplement: any) => {
    setSelectedSupplement(supplement);
  };

  const handleAddToStack = (supplement: any) => {
    setStackSupplements(prev => {
      if (prev.find(s => s.id === supplement.id)) {
        return prev; // Already in stack
      }
      return [...prev, {
        ...supplement,
        dosage: supplement.dosage || '1 capsule',
        timing: ['morning'],
        frequency: 'daily',
        price: supplement.price || Math.floor(Math.random() * 50) + 10,
        safetyRating: supplement.safetyRating || Math.floor(Math.random() * 3) + 7,
        evidenceLevel: supplement.evidenceLevel || Math.floor(Math.random() * 3) + 3,
        effects: supplement.effects || ['General Health'],
        warnings: supplement.warnings || [],
        interactions: [],
      }];
    });
  };

  const handleAddToProfile = (supplement: any) => {
    console.log('Adding to profile:', supplement);
    // This would trigger a refresh of the profile component
  };

  const handleProfileUpdate = (profile: any) => {
    console.log('Profile updated:', profile);
  };

  const handleStackUpdate = (stack: any[]) => {
    setStackSupplements(stack);
  };

  const handleSaveToProfile = (stack: any[]) => {
    console.log('Saving stack to profile:', stack);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white shadow-sm border-b border-gray-200"
      >
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold cosmic-gradient bg-clip-text text-transparent">
                🚀 Enhanced Supplement Platform
              </h1>
              <p className="text-gray-600 mt-1">
                AI-powered supplement discovery with Neo4j knowledge graphs and user profiling
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="cosmic-badge cosmic-badge--success">
                <SparklesIcon className="w-4 h-4 mr-1" />
                AI Enhanced
              </div>
              <div className="cosmic-badge cosmic-badge--primary">
                <LinkIcon className="w-4 h-4 mr-1" />
                Neo4j Powered
              </div>
              <div className="text-sm text-gray-600">
                Demo User: <span className="font-medium">{demoUser.name}</span>
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Tab Navigation */}
      <div className="max-w-7xl mx-auto px-6 py-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="cosmic-card mb-6"
        >
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`p-4 rounded-lg text-left transition-all ${
                    activeTab === tab.id
                      ? 'bg-primary-50 border-2 border-primary-200 text-primary-700'
                      : 'bg-gray-50 border-2 border-transparent text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  <div className="flex items-center space-x-3 mb-2">
                    <Icon className={`w-6 h-6 ${
                      activeTab === tab.id ? 'text-primary-600' : 'text-gray-400'
                    }`} />
                    <h3 className="font-semibold">{tab.name}</h3>
                  </div>
                  <p className="text-sm opacity-75">{tab.description}</p>
                </button>
              );
            })}
          </div>
        </motion.div>

        {/* Tab Content */}
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3 }}
          className="min-h-[600px]"
        >
          {activeTab === 'profile' && (
            <div className="cosmic-card">
              <div className="mb-6">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">User Profile Management</h2>
                <p className="text-gray-600">
                  Manage your health goals, current supplements, and preferences. The system uses this information 
                  to provide personalized recommendations and optimize your supplement stack.
                </p>
              </div>
              <EnhancedUserProfile
                userId={demoUser.id}
                onSupplementAdd={handleAddToProfile}
                onProfileUpdate={handleProfileUpdate}
              />
            </div>
          )}

          {activeTab === 'search' && (
            <div className="cosmic-card">
              <div className="mb-6">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">AI-Powered Supplement Search</h2>
                <p className="text-gray-600">
                  Search for supplements with AI-powered recommendations based on your profile. 
                  Results are personalized using Neo4j graph relationships and user preferences.
                </p>
              </div>
              <CosmicSupplementSearch
                userId={demoUser.id}
                onSupplementSelect={handleSupplementSelect}
                onAddToStack={handleAddToStack}
                onAddToProfile={handleAddToProfile}
                showProfileIntegration={true}
              />
            </div>
          )}

          {activeTab === 'stack' && (
            <div className="cosmic-card">
              <div className="mb-6">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">Intelligent Stack Builder</h2>
                <p className="text-gray-600">
                  Build and optimize your supplement stack with AI analysis. The system analyzes 
                  interactions, costs, and effectiveness using Neo4j graph data.
                </p>
              </div>
              <CosmicStackBuilder
                userId={demoUser.id}
                initialStack={stackSupplements}
                onStackUpdate={handleStackUpdate}
                onSaveToProfile={handleSaveToProfile}
                showProfileIntegration={true}
              />
            </div>
          )}

          {activeTab === 'graph' && (
            <div className="cosmic-card">
              <div className="mb-6">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">Interactive Knowledge Graph</h2>
                <p className="text-gray-600">
                  Explore supplement relationships, interactions, and research connections through 
                  an interactive Neo4j-powered knowledge graph visualization.
                </p>
              </div>
              <div className="h-[600px] w-full">
                <EnhancedKnowledgeGraph
                  userId={demoUser.id}
                  width={800}
                  height={600}
                  onNodeClick={handleSupplementSelect}
                  onNodeHover={(node) => console.log('Hovering:', node?.name)}
                  showControls={true}
                  enableUserProfile={true}
                />
              </div>
            </div>
          )}
        </motion.div>

        {/* Feature Highlights */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6"
        >
          <div className="cosmic-card cosmic-card--primary">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">🧠 AI-Powered Intelligence</h3>
            <ul className="text-sm text-gray-600 space-y-2">
              <li>• Personalized supplement recommendations</li>
              <li>• Intelligent interaction analysis</li>
              <li>• Stack optimization algorithms</li>
              <li>• Real-time safety assessments</li>
            </ul>
          </div>

          <div className="cosmic-card cosmic-card--success">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">🔗 Neo4j Graph Database</h3>
            <ul className="text-sm text-gray-600 space-y-2">
              <li>• Complex relationship modeling</li>
              <li>• Real-time graph analytics</li>
              <li>• Advanced query capabilities</li>
              <li>• Scalable knowledge representation</li>
            </ul>
          </div>

          <div className="cosmic-card cosmic-card--warning">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">👤 User-Centric Design</h3>
            <ul className="text-sm text-gray-600 space-y-2">
              <li>• Comprehensive user profiling</li>
              <li>• Preference-based filtering</li>
              <li>• Goal-oriented recommendations</li>
              <li>• Seamless profile integration</li>
            </ul>
          </div>
        </motion.div>

        {/* Technical Implementation Notes */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="mt-8 cosmic-card bg-gray-50"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-4">🔧 Technical Implementation</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Backend Enhancements:</h4>
              <ul className="text-gray-600 space-y-1">
                <li>• Enhanced GraphService with advanced Neo4j queries</li>
                <li>• UserProfileService for comprehensive user management</li>
                <li>• Real-time stack analysis with graph algorithms</li>
                <li>• Personalized recommendation engine</li>
                <li>• RESTful API with validation and error handling</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Frontend Features:</h4>
              <ul className="text-gray-600 space-y-1">
                <li>• Interactive D3.js graph visualization</li>
                <li>• Real-time profile integration</li>
                <li>• Advanced search with AI recommendations</li>
                <li>• Drag-and-drop stack builder</li>
                <li>• Responsive design with smooth animations</li>
              </ul>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default EnhancedSupplementDemo;
