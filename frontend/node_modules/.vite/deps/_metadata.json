{"hash": "5bea7830", "configHash": "ee8b8e8e", "lockfileHash": "4399deda", "browserHash": "b43cfb00", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "9f2d7f13", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "d7366ac1", "needsInterop": true}, "d3": {"src": "../../d3/src/index.js", "file": "d3.js", "fileHash": "682dcc67", "needsInterop": false}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "89bb6062", "needsInterop": false}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "d0732a4b", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "21f7a65c", "needsInterop": true}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "23be7344", "needsInterop": false}, "@tanstack/react-query-devtools": {"src": "../../@tanstack/react-query-devtools/build/modern/index.js", "file": "@tanstack_react-query-devtools.js", "fileHash": "33d40fbf", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "c61df79c", "needsInterop": false}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "4ae7af70", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "4b218ae4", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "97799a39", "needsInterop": true}, "react-dropzone": {"src": "../../react-dropzone/dist/es/index.js", "file": "react-dropzone.js", "fileHash": "c59ca32f", "needsInterop": false}, "react-hot-toast": {"src": "../../react-hot-toast/dist/index.mjs", "file": "react-hot-toast.js", "fileHash": "98dc055f", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "fc7f513f", "needsInterop": false}, "sonner": {"src": "../../sonner/dist/index.mjs", "file": "sonner.js", "fileHash": "abe75be9", "needsInterop": false}, "use-debounce": {"src": "../../use-debounce/dist/index.module.js", "file": "use-debounce.js", "fileHash": "ad07563d", "needsInterop": false}, "socket.io-client": {"src": "../../socket.io-client/build/esm/index.js", "file": "socket__io-client.js", "fileHash": "d19a425a", "needsInterop": false}, "@heroicons/react/24/outline": {"src": "../../@heroicons/react/24/outline/esm/index.js", "file": "@heroicons_react_24_outline.js", "fileHash": "c5be72c2", "needsInterop": false}, "lodash": {"src": "../../lodash/lodash.js", "file": "lodash.js", "fileHash": "d0cc468f", "needsInterop": true}}, "chunks": {"HH7B3BHX-Z72QSRLZ": {"file": "HH7B3BHX-Z72QSRLZ.js"}, "JZI2RDCT-QKPLJTR2": {"file": "JZI2RDCT-QKPLJTR2.js"}, "chunk-DERXUTWC": {"file": "chunk-DERXUTWC.js"}, "chunk-276SZO74": {"file": "chunk-276SZO74.js"}, "chunk-UQB3IAMY": {"file": "chunk-UQB3IAMY.js"}, "chunk-EUYC5BH3": {"file": "chunk-EUYC5BH3.js"}, "chunk-CRNJR6QK": {"file": "chunk-CRNJR6QK.js"}, "chunk-ZMLY2J2T": {"file": "chunk-ZMLY2J2T.js"}, "chunk-4B2QHNJT": {"file": "chunk-4B2QHNJT.js"}}}