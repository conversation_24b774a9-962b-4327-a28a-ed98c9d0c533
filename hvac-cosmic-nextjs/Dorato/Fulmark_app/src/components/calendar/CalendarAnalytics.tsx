'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  Calendar, 
  Clock, 
  Users, 
  Brain,
  Target,
  Zap,
  Loader2,
  RefreshCw
} from 'lucide-react';
import { useToast } from '../ui/use-toast';

interface CalendarAnalyticsData {
  totalEvents: number;
  futureEvents: number;
  recentEvents: number;
  eventsByType: Record<string, number>;
  averageSentiment: number;
  avgConfidence: number;
  avgPriority: number;
  customerMatchRate: number;
  lastEventDate: string | null;
  nextEventDate: string | null;
}

interface CalendarAnalyticsProps {
  customerId?: string;
  timeRange?: 'week' | 'month' | 'quarter' | 'year';
}

export function CalendarAnalytics({ customerId, timeRange = 'month' }: CalendarAnalyticsProps) {
  const [analytics, setAnalytics] = useState<CalendarAnalyticsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    fetchAnalytics();
  }, [customerId, timeRange]);

  const fetchAnalytics = async () => {
    try {
      setIsLoading(true);
      
      let url = '/api/calendar/events';
      if (customerId) {
        url = `/api/customers/${customerId}/calendar`;
      }
      
      // Calculate date range
      const now = new Date();
      let startDate: Date;
      
      switch (timeRange) {
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'month':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        case 'quarter':
          startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
          break;
        case 'year':
          startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
          break;
        default:
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      }
      
      const params = new URLSearchParams({
        startDate: startDate.toISOString(),
        limit: '1000'
      });
      
      const response = await fetch(`${url}?${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch calendar analytics');
      }

      const data = await response.json();
      setAnalytics(data.statistics);
    } catch (error) {
      console.error('Error fetching calendar analytics:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch calendar analytics',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const refreshAnalytics = async () => {
    setIsRefreshing(true);
    await fetchAnalytics();
    setIsRefreshing(false);
  };

  const getEventTypeColor = (type: string) => {
    const colors = {
      sales_meeting: 'text-green-600',
      support_call: 'text-blue-600',
      installation: 'text-purple-600',
      consultation: 'text-orange-600',
      follow_up: 'text-yellow-600',
      maintenance: 'text-red-600',
      training: 'text-indigo-600',
      other: 'text-gray-600',
    };
    return colors[type as keyof typeof colors] || colors.other;
  };

  const getSentimentColor = (sentiment: number) => {
    if (sentiment > 0.2) return 'text-green-600';
    if (sentiment < -0.2) return 'text-red-600';
    return 'text-gray-600';
  };

  const getSentimentIcon = (sentiment: number) => {
    if (sentiment > 0.2) return <TrendingUp className="w-4 h-4" />;
    if (sentiment < -0.2) return <TrendingDown className="w-4 h-4" />;
    return <div className="w-4 h-4 rounded-full bg-gray-400" />;
  };

  const formatTimeRange = (range: string) => {
    const labels = {
      week: 'Last 7 days',
      month: 'Last 30 days',
      quarter: 'Last 90 days',
      year: 'Last year'
    };
    return labels[range as keyof typeof labels] || labels.month;
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-6">
          <Loader2 className="w-6 h-6 animate-spin mr-2" />
          Loading analytics...
        </CardContent>
      </Card>
    );
  }

  if (!analytics) {
    return (
      <Card>
        <CardContent className="text-center p-6">
          <BarChart3 className="w-12 h-12 mx-auto text-gray-400 mb-4" />
          <p className="text-gray-600">No analytics data available</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Calendar Analytics</h2>
          <p className="text-gray-600">
            {formatTimeRange(timeRange)} • {customerId ? 'Customer specific' : 'All events'}
          </p>
        </div>
        <Button
          onClick={refreshAnalytics}
          disabled={isRefreshing}
          variant="outline"
          size="sm"
        >
          {isRefreshing ? (
            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
          ) : (
            <RefreshCw className="w-4 h-4 mr-2" />
          )}
          Refresh
        </Button>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Events</p>
                <p className="text-2xl font-bold text-blue-600">{analytics.totalEvents}</p>
              </div>
              <Calendar className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Upcoming</p>
                <p className="text-2xl font-bold text-green-600">{analytics.futureEvents}</p>
              </div>
              <Clock className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">AI Confidence</p>
                <p className="text-2xl font-bold text-purple-600">
                  {Math.round(analytics.avgConfidence * 100)}%
                </p>
              </div>
              <Brain className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Customer Match</p>
                <p className="text-2xl font-bold text-orange-600">
                  {Math.round(analytics.customerMatchRate * 100)}%
                </p>
              </div>
              <Target className="w-8 h-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Event Types Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="w-5 h-5 mr-2" />
              Event Types
            </CardTitle>
            <CardDescription>Distribution of event types</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(analytics.eventsByType)
                .sort(([, a], [, b]) => b - a)
                .map(([type, count]) => {
                  const percentage = analytics.totalEvents > 0 
                    ? Math.round((count / analytics.totalEvents) * 100) 
                    : 0;
                  
                  return (
                    <div key={type} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline" className={getEventTypeColor(type)}>
                          {type.replace('_', ' ').toUpperCase()}
                        </Badge>
                        <span className="text-sm text-gray-600">{count} events</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="w-20 bg-gray-200 rounded-full h-2">
                          <div 
                            className={`h-2 rounded-full ${getEventTypeColor(type).replace('text-', 'bg-')}`}
                            style={{ width: `${percentage}%` }}
                          />
                        </div>
                        <span className="text-sm font-medium">{percentage}%</span>
                      </div>
                    </div>
                  );
                })}
            </div>
          </CardContent>
        </Card>

        {/* Sentiment & Quality Metrics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Zap className="w-5 h-5 mr-2" />
              Quality Metrics
            </CardTitle>
            <CardDescription>AI analysis quality indicators</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Average Sentiment */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  {getSentimentIcon(analytics.averageSentiment)}
                  <span className="text-sm font-medium">Average Sentiment</span>
                </div>
                <div className={`text-lg font-bold ${getSentimentColor(analytics.averageSentiment)}`}>
                  {analytics.averageSentiment > 0 ? '+' : ''}{analytics.averageSentiment.toFixed(2)}
                </div>
              </div>

              {/* Average Priority */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Target className="w-4 h-4 text-blue-600" />
                  <span className="text-sm font-medium">Average Priority</span>
                </div>
                <div className="text-lg font-bold text-blue-600">
                  {analytics.avgPriority.toFixed(1)}/5
                </div>
              </div>

              {/* AI Confidence */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Brain className="w-4 h-4 text-purple-600" />
                  <span className="text-sm font-medium">AI Confidence</span>
                </div>
                <div className="text-lg font-bold text-purple-600">
                  {Math.round(analytics.avgConfidence * 100)}%
                </div>
              </div>

              {/* Customer Match Rate */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Users className="w-4 h-4 text-green-600" />
                  <span className="text-sm font-medium">Customer Match Rate</span>
                </div>
                <div className="text-lg font-bold text-green-600">
                  {Math.round(analytics.customerMatchRate * 100)}%
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Timeline Info */}
      <Card>
        <CardHeader>
          <CardTitle>Timeline Overview</CardTitle>
          <CardDescription>Recent and upcoming event timeline</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-700 mb-2">Last Event</h4>
              <p className="text-sm text-gray-600">
                {analytics.lastEventDate 
                  ? new Date(analytics.lastEventDate).toLocaleDateString('pl-PL', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })
                  : 'No events found'
                }
              </p>
            </div>
            <div>
              <h4 className="font-medium text-gray-700 mb-2">Next Event</h4>
              <p className="text-sm text-gray-600">
                {analytics.nextEventDate 
                  ? new Date(analytics.nextEventDate).toLocaleDateString('pl-PL', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })
                  : 'No upcoming events'
                }
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
