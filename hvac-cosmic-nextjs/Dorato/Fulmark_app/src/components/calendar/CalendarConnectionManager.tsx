'use client';

import { useState, useEffect } from 'react';
import { Button } from '../ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Loader2, Calendar, CheckCircle, XCircle, RefreshCw, AlertCircle } from 'lucide-react';
import { useToast } from '../ui/use-toast';

interface CalendarConnection {
  id: string;
  isActive: boolean;
  lastSyncAt: string | null;
  syncStatus: 'pending' | 'syncing' | 'completed' | 'error';
  errorMessage: string | null;
  createdAt: string;
  scopes: string[];
}

interface ConnectionStatus {
  connections: CalendarConnection[];
  hasActiveConnection: boolean;
}

export function CalendarConnectionManager() {
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isConnecting, setIsConnecting] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    fetchConnectionStatus();
  }, []);

  const fetchConnectionStatus = async () => {
    try {
      const response = await fetch('/api/calendar/connect', {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch connection status');
      }

      const data = await response.json();
      setConnectionStatus(data);
    } catch (error) {
      console.error('Error fetching connection status:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch calendar connection status',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleConnect = async () => {
    setIsConnecting(true);
    try {
      const response = await fetch('/api/calendar/connect');
      
      if (!response.ok) {
        throw new Error('Failed to initiate connection');
      }

      const data = await response.json();
      
      // Redirect to Microsoft OAuth
      window.location.href = data.authUrl;
    } catch (error) {
      console.error('Error connecting calendar:', error);
      toast({
        title: 'Connection Failed',
        description: 'Failed to connect to Outlook calendar',
        variant: 'destructive',
      });
      setIsConnecting(false);
    }
  };

  const handleSync = async (connectionId?: string) => {
    setIsSyncing(true);
    try {
      const response = await fetch('/api/calendar/sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          connectionId,
          syncType: 'incremental',
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to start sync');
      }

      const data = await response.json();
      
      toast({
        title: 'Sync Started',
        description: data.message,
      });

      // Refresh status after a short delay
      setTimeout(() => {
        fetchConnectionStatus();
      }, 2000);
    } catch (error) {
      console.error('Error syncing calendar:', error);
      toast({
        title: 'Sync Failed',
        description: 'Failed to sync calendar events',
        variant: 'destructive',
      });
    } finally {
      setIsSyncing(false);
    }
  };

  const getSyncStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="default" className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Completed</Badge>;
      case 'syncing':
        return <Badge variant="default" className="bg-blue-100 text-blue-800"><Loader2 className="w-3 h-3 mr-1 animate-spin" />Syncing</Badge>;
      case 'error':
        return <Badge variant="destructive"><XCircle className="w-3 h-3 mr-1" />Error</Badge>;
      case 'pending':
        return <Badge variant="secondary"><AlertCircle className="w-3 h-3 mr-1" />Pending</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleString();
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-6">
          <Loader2 className="w-6 h-6 animate-spin mr-2" />
          Loading calendar connection status...
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="w-5 h-5 mr-2" />
            Outlook Calendar Integration
          </CardTitle>
          <CardDescription>
            Connect your Outlook calendar to automatically sync events and enhance customer profiles
          </CardDescription>
        </CardHeader>
        <CardContent>
          {!connectionStatus?.hasActiveConnection ? (
            <div className="text-center py-6">
              <Calendar className="w-12 h-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium mb-2">No Calendar Connected</h3>
              <p className="text-gray-600 mb-4">
                Connect your Outlook calendar to automatically sync events and get AI-powered insights
              </p>
              <Button 
                onClick={handleConnect} 
                disabled={isConnecting}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {isConnecting ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Connecting...
                  </>
                ) : (
                  <>
                    <Calendar className="w-4 h-4 mr-2" />
                    Connect Outlook Calendar
                  </>
                )}
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
                  <span className="font-medium">Calendar Connected</span>
                </div>
                <Button
                  onClick={() => handleSync()}
                  disabled={isSyncing}
                  variant="outline"
                  size="sm"
                >
                  {isSyncing ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Syncing...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2" />
                      Sync Now
                    </>
                  )}
                </Button>
              </div>

              {connectionStatus.connections.map((connection) => (
                <div key={connection.id} className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium">Connection {connection.id.slice(0, 8)}</span>
                      {getSyncStatusBadge(connection.syncStatus)}
                    </div>
                    <Badge variant={connection.isActive ? "default" : "secondary"}>
                      {connection.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                  </div>

                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Last Sync:</span>
                      <div className="font-medium">{formatDate(connection.lastSyncAt)}</div>
                    </div>
                    <div>
                      <span className="text-gray-600">Connected:</span>
                      <div className="font-medium">{formatDate(connection.createdAt)}</div>
                    </div>
                  </div>

                  {connection.errorMessage && (
                    <div className="bg-red-50 border border-red-200 rounded p-3">
                      <div className="flex items-center">
                        <XCircle className="w-4 h-4 text-red-600 mr-2" />
                        <span className="text-red-800 text-sm font-medium">Error</span>
                      </div>
                      <p className="text-red-700 text-sm mt-1">{connection.errorMessage}</p>
                    </div>
                  )}

                  {connection.scopes && connection.scopes.length > 0 && (
                    <div>
                      <span className="text-gray-600 text-sm">Permissions:</span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {connection.scopes.map((scope) => (
                          <Badge key={scope} variant="outline" className="text-xs">
                            {scope.replace('https://graph.microsoft.com/', '')}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
