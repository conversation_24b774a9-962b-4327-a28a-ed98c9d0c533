'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { 
  Calendar, 
  ChevronLeft, 
  ChevronRight, 
  Clock, 
  MapPin, 
  Users, 
  Brain,
  TrendingUp,
  TrendingDown,
  Loader2
} from 'lucide-react';
import { useToast } from '../ui/use-toast';

interface CalendarEvent {
  id: string;
  subject: string;
  startTime: string | null;
  endTime: string | null;
  location: string | null;
  attendees: any[];
  eventType: string | null;
  priority: number;
  confidenceScore: string;
  sentimentScore: string | null;
  customerId?: string;
  isAllDay: boolean;
}

interface CalendarMonthViewProps {
  customerId?: string;
}

export function CalendarMonthView({ customerId }: CalendarMonthViewProps) {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [events, setEvents] = useState<CalendarEvent[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    fetchEventsForMonth();
  }, [currentDate, customerId]);

  const fetchEventsForMonth = async () => {
    try {
      setIsLoading(true);
      
      // Get first and last day of the month
      const firstDay = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
      const lastDay = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
      
      let url = '/api/calendar/events';
      if (customerId) {
        url = `/api/customers/${customerId}/calendar`;
      }
      
      const params = new URLSearchParams({
        startDate: firstDay.toISOString(),
        endDate: lastDay.toISOString(),
        limit: '1000'
      });
      
      const response = await fetch(`${url}?${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch calendar events');
      }

      const data = await response.json();
      setEvents(data.events || []);
    } catch (error) {
      console.error('Error fetching calendar events:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch calendar events',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (direction === 'prev') {
      newDate.setMonth(newDate.getMonth() - 1);
    } else {
      newDate.setMonth(newDate.getMonth() + 1);
    }
    setCurrentDate(newDate);
  };

  const getEventsForDate = (date: Date) => {
    return events.filter(event => {
      if (!event.startTime) return false;
      const eventDate = new Date(event.startTime);
      return eventDate.toDateString() === date.toDateString();
    });
  };

  const getEventTypeBadge = (eventType: string | null, size: 'sm' | 'xs' = 'xs') => {
    const type = eventType || 'other';
    const colors = {
      sales_meeting: 'bg-green-100 text-green-800',
      support_call: 'bg-blue-100 text-blue-800',
      installation: 'bg-purple-100 text-purple-800',
      consultation: 'bg-orange-100 text-orange-800',
      follow_up: 'bg-yellow-100 text-yellow-800',
      maintenance: 'bg-red-100 text-red-800',
      training: 'bg-indigo-100 text-indigo-800',
      other: 'bg-gray-100 text-gray-800',
    };

    return (
      <Badge 
        className={`${colors[type as keyof typeof colors] || colors.other} ${size === 'xs' ? 'text-xs px-1 py-0' : ''}`}
      >
        {size === 'xs' ? type.charAt(0).toUpperCase() : type.replace('_', ' ').toUpperCase()}
      </Badge>
    );
  };

  const getSentimentIcon = (sentimentScore: string | null, size: 'sm' | 'xs' = 'xs') => {
    if (!sentimentScore) return null;
    const score = parseFloat(sentimentScore);
    const iconSize = size === 'xs' ? 'w-3 h-3' : 'w-4 h-4';
    
    if (score > 0.2) {
      return <TrendingUp className={`${iconSize} text-green-600`} />;
    } else if (score < -0.2) {
      return <TrendingDown className={`${iconSize} text-red-600`} />;
    }
    return <div className={`${iconSize} rounded-full bg-gray-400`} />;
  };

  // Generate calendar grid
  const generateCalendarDays = () => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    
    const firstDayOfMonth = new Date(year, month, 1);
    const lastDayOfMonth = new Date(year, month + 1, 0);
    const firstDayOfWeek = firstDayOfMonth.getDay();
    
    const days = [];
    
    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDayOfWeek; i++) {
      days.push(null);
    }
    
    // Add all days of the month
    for (let day = 1; day <= lastDayOfMonth.getDate(); day++) {
      days.push(new Date(year, month, day));
    }
    
    return days;
  };

  const formatMonthYear = (date: Date) => {
    return date.toLocaleDateString('pl-PL', { 
      month: 'long', 
      year: 'numeric' 
    });
  };

  const isToday = (date: Date) => {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  };

  const isSelected = (date: Date) => {
    return selectedDate && date.toDateString() === selectedDate.toDateString();
  };

  const calendarDays = generateCalendarDays();
  const weekDays = ['Nd', 'Pn', 'Wt', 'Śr', 'Cz', 'Pt', 'Sb'];

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-6">
          <Loader2 className="w-6 h-6 animate-spin mr-2" />
          Loading calendar...
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Calendar Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center">
                <Calendar className="w-5 h-5 mr-2" />
                {formatMonthYear(currentDate)}
              </CardTitle>
              <CardDescription>
                {customerId ? 'Customer calendar events' : 'All calendar events'} with AI insights
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigateMonth('prev')}
              >
                <ChevronLeft className="w-4 h-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentDate(new Date())}
              >
                Today
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigateMonth('next')}
              >
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Calendar Grid */}
          <div className="grid grid-cols-7 gap-1">
            {/* Week day headers */}
            {weekDays.map((day) => (
              <div key={day} className="p-2 text-center text-sm font-medium text-gray-500">
                {day}
              </div>
            ))}
            
            {/* Calendar days */}
            {calendarDays.map((date, index) => {
              if (!date) {
                return <div key={index} className="p-2 h-24"></div>;
              }
              
              const dayEvents = getEventsForDate(date);
              const hasEvents = dayEvents.length > 0;
              
              return (
                <div
                  key={date.toISOString()}
                  className={`
                    p-1 h-24 border border-gray-200 cursor-pointer transition-colors
                    ${isToday(date) ? 'bg-blue-50 border-blue-300' : ''}
                    ${isSelected(date) ? 'bg-blue-100 border-blue-400' : ''}
                    ${hasEvents ? 'bg-green-50' : ''}
                    hover:bg-gray-50
                  `}
                  onClick={() => setSelectedDate(date)}
                >
                  <div className="flex flex-col h-full">
                    <div className={`text-sm ${isToday(date) ? 'font-bold text-blue-600' : ''}`}>
                      {date.getDate()}
                    </div>
                    <div className="flex-1 overflow-hidden">
                      {dayEvents.slice(0, 2).map((event, eventIndex) => (
                        <div
                          key={event.id}
                          className="flex items-center space-x-1 mb-1 text-xs"
                        >
                          {getEventTypeBadge(event.eventType, 'xs')}
                          {getSentimentIcon(event.sentimentScore, 'xs')}
                          {event.customerId && (
                            <Brain className="w-2 h-2 text-purple-600" />
                          )}
                        </div>
                      ))}
                      {dayEvents.length > 2 && (
                        <div className="text-xs text-gray-500">
                          +{dayEvents.length - 2} more
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Selected Date Events */}
      {selectedDate && (
        <Card>
          <CardHeader>
            <CardTitle>
              Events for {selectedDate.toLocaleDateString('pl-PL', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {(() => {
              const dayEvents = getEventsForDate(selectedDate);
              
              if (dayEvents.length === 0) {
                return (
                  <div className="text-center py-6">
                    <Calendar className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                    <p className="text-gray-600">No events scheduled for this day</p>
                  </div>
                );
              }
              
              return (
                <div className="space-y-3">
                  {dayEvents.map((event) => (
                    <div key={event.id} className="border rounded-lg p-3 space-y-2">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h4 className="font-medium">{event.subject || 'Untitled Event'}</h4>
                          <div className="flex items-center space-x-3 text-sm text-gray-600 mt-1">
                            <div className="flex items-center">
                              <Clock className="w-3 h-3 mr-1" />
                              {event.startTime && new Date(event.startTime).toLocaleTimeString([], { 
                                hour: '2-digit', 
                                minute: '2-digit' 
                              })}
                            </div>
                            {event.location && (
                              <div className="flex items-center">
                                <MapPin className="w-3 h-3 mr-1" />
                                {event.location}
                              </div>
                            )}
                            {event.attendees.length > 0 && (
                              <div className="flex items-center">
                                <Users className="w-3 h-3 mr-1" />
                                {event.attendees.length}
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {getSentimentIcon(event.sentimentScore, 'sm')}
                          {getEventTypeBadge(event.eventType, 'sm')}
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <div>
                          Confidence: {Math.round(parseFloat(event.confidenceScore) * 100)}%
                        </div>
                        <div>
                          Priority: {event.priority}/5
                        </div>
                        {event.customerId && (
                          <div className="flex items-center">
                            <Brain className="w-3 h-3 mr-1" />
                            Customer Matched
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              );
            })()}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
