'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Activity,
  AlertTriangle,
  BarChart3,
  Clock,
  Gauge,
  LineChart,
  MoreVertical,
  Thermometer,
  Wind,
  Zap,
  XCircle,
} from 'lucide-react';
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Line,
  Bar,
  BarChart,
} from 'recharts';

interface SystemMetric {
  id: string;
  name: string;
  value: number;
  unit: string;
  status: 'normal' | 'warning' | 'critical';
  trend: 'up' | 'down' | 'stable';
  threshold: {
    warning: number;
    critical: number;
  };
}

interface PerformanceData {
  timestamp: string;
  temperature: number;
  humidity: number;
  pressure: number;
  flowRate: number;
  powerConsumption: number;
  efficiency: number;
}

interface SystemAlert {
  id: string;
  type: 'performance' | 'maintenance' | 'safety' | 'efficiency';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: string;
  status: 'active' | 'acknowledged' | 'resolved';
  systemId: string;
  systemName: string;
}

interface SystemPerformanceProps {
  metrics: SystemMetric[];
  performanceHistory: PerformanceData[];
  alerts: SystemAlert[];
}

const SystemPerformance: React.FC<SystemPerformanceProps> = ({
  metrics,
  performanceHistory,
  alerts,
}) => {
  const [selectedMetric, setSelectedMetric] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState<'1h' | '24h' | '7d' | '30d'>('24h');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'normal':
        return 'text-green-500';
      case 'warning':
        return 'text-yellow-500';
      case 'critical':
        return 'text-red-500';
      default:
        return 'text-gray-500';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-100 text-red-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const renderMetrics = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {metrics.map((metric) => (
        <motion.div
          key={metric.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className={`bg-white rounded-lg shadow p-6 cursor-pointer ${
            selectedMetric === metric.id ? 'ring-2 ring-blue-500' : ''
          }`}
          onClick={() => setSelectedMetric(metric.id)}
        >
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <Gauge className="w-6 h-6 text-blue-500" />
              <h3 className="text-lg font-semibold">{metric.name}</h3>
            </div>
            <button className="text-gray-400 hover:text-gray-600">
              <MoreVertical className="w-5 h-5" />
            </button>
          </div>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-2xl font-bold">
                {metric.value} {metric.unit}
              </span>
              <span
                className={`text-sm font-medium ${getStatusColor(
                  metric.status
                )}`}
              >
                {metric.status}
              </span>
            </div>
            <div className="flex items-center justify-between text-sm text-gray-500">
              <span>Warning: {metric.threshold.warning}</span>
              <span>Critical: {metric.threshold.critical}</span>
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  );

  const renderPerformanceChart = () => (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-semibold">Performance History</h2>
        <div className="flex space-x-2">
          {(['1h', '24h', '7d', '30d'] as const).map((range) => (
            <button
              key={range}
              onClick={() => setTimeRange(range)}
              className={`px-3 py-1 rounded-md ${
                timeRange === range
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              {range}
            </button>
          ))}
        </div>
      </div>
      <div className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart data={performanceHistory}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="timestamp" />
            <YAxis />
            <Tooltip />
            <Area
              type="monotone"
              dataKey="temperature"
              stroke="#8884d8"
              fill="#8884d8"
              fillOpacity={0.3}
            />
            <Area
              type="monotone"
              dataKey="humidity"
              stroke="#82ca9d"
              fill="#82ca9d"
              fillOpacity={0.3}
            />
            <Area
              type="monotone"
              dataKey="efficiency"
              stroke="#ffc658"
              fill="#ffc658"
              fillOpacity={0.3}
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>
    </div>
  );

  const renderAlerts = () => (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-semibold">System Alerts</h2>
        <button className="text-blue-600 hover:text-blue-800">
          View All
        </button>
      </div>
      <div className="space-y-4">
        {alerts.map((alert) => (
          <motion.div
            key={alert.id}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg"
          >
            <AlertTriangle
              className={`w-6 h-6 ${
                alert.severity === 'critical'
                  ? 'text-red-500'
                  : alert.severity === 'high'
                  ? 'text-orange-500'
                  : 'text-yellow-500'
              }`}
            />
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-medium">{alert.systemName}</h3>
                <span
                  className={`px-2 py-1 text-xs rounded-full ${getSeverityColor(
                    alert.severity
                  )}`}
                >
                  {alert.severity}
                </span>
              </div>
              <p className="text-sm text-gray-600 mt-1">{alert.message}</p>
              <div className="flex items-center justify-between mt-2">
                <span className="text-xs text-gray-500">
                  {alert.timestamp}
                </span>
                <span
                  className={`px-2 py-1 text-xs rounded-full ${getStatusColor(
                    alert.status
                  )}`}
                >
                  {alert.status}
                </span>
              </div>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );

  const renderDetailedMetrics = () => {
    if (!selectedMetric) return null;

    const metric = metrics.find((m) => m.id === selectedMetric);
    if (!metric) return null;

    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-semibold">{metric.name} Details</h2>
          <button
            onClick={() => setSelectedMetric(null)}
            className="text-gray-400 hover:text-gray-600"
          >
            <XCircle className="w-6 h-6" />
          </button>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-sm font-medium mb-4">Current Status</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Current Value</span>
                <span className="text-sm font-medium">
                  {metric.value} {metric.unit}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Status</span>
                <span
                  className={`text-sm font-medium ${getStatusColor(
                    metric.status
                  )}`}
                >
                  {metric.status}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Trend</span>
                <span className="text-sm font-medium">{metric.trend}</span>
              </div>
            </div>
          </div>
          <div>
            <h3 className="text-sm font-medium mb-4">Thresholds</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Warning Level</span>
                <span className="text-sm font-medium">
                  {metric.threshold.warning} {metric.unit}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Critical Level</span>
                <span className="text-sm font-medium">
                  {metric.threshold.critical} {metric.unit}
                </span>
              </div>
            </div>
          </div>
        </div>
        <div className="mt-6">
          <h3 className="text-sm font-medium mb-4">Historical Data</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={performanceHistory}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="timestamp" />
                <YAxis />
                <Tooltip />
                <Line
                  type="monotone"
                  dataKey={metric.name.toLowerCase()}
                  stroke="#8884d8"
                  strokeWidth={2}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {renderMetrics()}
      {renderPerformanceChart()}
      {renderAlerts()}
      {renderDetailedMetrics()}
    </div>
  );
};

export default SystemPerformance; 