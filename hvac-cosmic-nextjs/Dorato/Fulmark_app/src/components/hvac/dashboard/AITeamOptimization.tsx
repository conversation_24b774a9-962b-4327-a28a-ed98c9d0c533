'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Users,
  Brain,
  TrendingUp,
  TrendingDown,
  Target,
  Clock,
  Calendar,
  MapPin,
  Zap,
  Activity,
  BarChart3,
  AlertTriangle,
  CheckCircle,
  Loader2,
  <PERSON><PERSON>reshCw,
  Star,
  Award,
  Briefcase,
  Phone,
  Mail,
  Settings
} from 'lucide-react';

interface TeamMember {
  id: string;
  name: string;
  role: string;
  email: string;
  phone: string;
  avatar?: string;
  skills: string[];
  certifications: string[];
  location: string;
  status: 'available' | 'busy' | 'on-leave' | 'training';
  currentProject?: string;
  aiMetrics?: {
    performanceScore: number;
    efficiencyRating: number;
    customerSatisfaction: number;
    skillGrowth: number;
    workloadBalance: number;
    recommendations: string[];
  };
}

interface TeamOptimizationInsights {
  totalMembers: number;
  availableMembers: number;
  averagePerformance: number;
  teamEfficiency: number;
  skillCoverage: number;
  workloadDistribution: number;
  trainingNeeds: string[];
  optimizationSuggestions: string[];
  predictedCapacity: number;
}

const AITeamOptimization: React.FC = () => {
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [insights, setInsights] = useState<TeamOptimizationInsights | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [selectedMember, setSelectedMember] = useState<TeamMember | null>(null);
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterSkill, setFilterSkill] = useState('all');

  useEffect(() => {
    fetchTeamData();
  }, []);

  const fetchTeamData = async () => {
    try {
      setIsLoading(true);
      
      const [membersResponse, insightsResponse] = await Promise.all([
        fetch('/api/team/members'),
        fetch('/api/team/ai/insights')
      ]);
      
      const membersData = await membersResponse.json();
      const insightsData = await insightsResponse.json();
      
      setTeamMembers(membersData.members || []);
      setInsights(insightsData.insights || null);
      
    } catch (error) {
      console.error('Error fetching team data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const runTeamOptimization = async () => {
    try {
      setIsOptimizing(true);
      
      const response = await fetch('/api/team/ai/optimize', {
        method: 'POST',
      });
      
      if (!response.ok) throw new Error('Team optimization failed');
      
      const data = await response.json();
      
      // Refresh data
      await fetchTeamData();
      
      alert(`Team optimization completed! ${data.optimizationsApplied} optimizations applied`);
      
    } catch (error) {
      console.error('Error running team optimization:', error);
      alert('Failed to run team optimization');
    } finally {
      setIsOptimizing(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'text-green-600 bg-green-100';
      case 'busy': return 'text-blue-600 bg-blue-100';
      case 'on-leave': return 'text-gray-600 bg-gray-100';
      case 'training': return 'text-purple-600 bg-purple-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getPerformanceColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 75) return 'text-blue-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getSkillLevel = (score: number) => {
    if (score >= 90) return 'Expert';
    if (score >= 75) return 'Advanced';
    if (score >= 60) return 'Intermediate';
    return 'Beginner';
  };

  const filteredMembers = teamMembers.filter(member => {
    const matchesStatus = filterStatus === 'all' || member.status === filterStatus;
    const matchesSkill = filterSkill === 'all' || member.skills.includes(filterSkill);
    return matchesStatus && matchesSkill;
  });

  const allSkills = Array.from(new Set(teamMembers.flatMap(member => member.skills)));
  const statuses = ['all', 'available', 'busy', 'on-leave', 'training'];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-12">
        <Loader2 className="w-8 h-8 animate-spin text-blue-600 mr-3" />
        <span className="text-gray-600">Loading team optimization data...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">AI Team Optimization</h2>
          <p className="text-gray-600">
            Intelligent team management with performance analytics and workload optimization
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={fetchTeamData}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </button>
          <button
            onClick={runTeamOptimization}
            disabled={isOptimizing}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
          >
            {isOptimizing ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Brain className="w-4 h-4 mr-2" />
            )}
            {isOptimizing ? 'Optimizing...' : 'Run AI Optimization'}
          </button>
        </div>
      </div>

      {/* Team Insights */}
      {insights && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-100">Team Members</p>
                <p className="text-3xl font-bold">{insights.totalMembers}</p>
              </div>
              <Users className="w-8 h-8 text-blue-200" />
            </div>
            <div className="mt-4 flex items-center">
              <CheckCircle className="w-4 h-4 mr-1" />
              <span className="text-sm">{insights.availableMembers} available</span>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-6 text-white"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-100">Team Performance</p>
                <p className="text-3xl font-bold">{insights.averagePerformance}%</p>
              </div>
              <Target className="w-8 h-8 text-green-200" />
            </div>
            <div className="mt-4 flex items-center">
              <TrendingUp className="w-4 h-4 mr-1" />
              <span className="text-sm">Above target</span>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-6 text-white"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-100">Team Efficiency</p>
                <p className="text-3xl font-bold">{insights.teamEfficiency}%</p>
              </div>
              <Zap className="w-8 h-8 text-purple-200" />
            </div>
            <div className="mt-4 flex items-center">
              <Brain className="w-4 h-4 mr-1" />
              <span className="text-sm">AI Optimized</span>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg p-6 text-white"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-100">Skill Coverage</p>
                <p className="text-3xl font-bold">{insights.skillCoverage}%</p>
              </div>
              <Award className="w-8 h-8 text-orange-200" />
            </div>
            <div className="mt-4 flex items-center">
              <Activity className="w-4 h-4 mr-1" />
              <span className="text-sm">Comprehensive</span>
            </div>
          </motion.div>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Filter by Status
            </label>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {statuses.map(status => (
                <option key={status} value={status}>
                  {status === 'all' ? 'All Statuses' : status.charAt(0).toUpperCase() + status.slice(1).replace('-', ' ')}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Filter by Skill
            </label>
            <select
              value={filterSkill}
              onChange={(e) => setFilterSkill(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Skills</option>
              {allSkills.map(skill => (
                <option key={skill} value={skill}>{skill}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Team Members Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredMembers.map((member) => (
          <motion.div
            key={member.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer"
            onClick={() => setSelectedMember(member)}
          >
            {/* Member Header */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 font-semibold text-lg">
                    {member.name.split(' ').map(n => n[0]).join('')}
                  </span>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">{member.name}</h3>
                  <p className="text-sm text-gray-600">{member.role}</p>
                </div>
              </div>
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs ${getStatusColor(member.status)}`}>
                {member.status.replace('-', ' ').toUpperCase()}
              </span>
            </div>

            {/* Contact Info */}
            <div className="space-y-2 mb-4">
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Mail className="w-3 h-3" />
                <span>{member.email}</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Phone className="w-3 h-3" />
                <span>{member.phone}</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <MapPin className="w-3 h-3" />
                <span>{member.location}</span>
              </div>
            </div>

            {/* AI Metrics */}
            {member.aiMetrics && (
              <div className="mb-4 p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700">AI Performance</span>
                  <span className={`text-sm font-bold ${getPerformanceColor(member.aiMetrics.performanceScore)}`}>
                    {member.aiMetrics.performanceScore}%
                  </span>
                </div>
                <div className="grid grid-cols-2 gap-2 text-xs text-gray-600">
                  <div>Efficiency: {member.aiMetrics.efficiencyRating}%</div>
                  <div>Satisfaction: {member.aiMetrics.customerSatisfaction}%</div>
                  <div>Growth: {member.aiMetrics.skillGrowth}%</div>
                  <div>Balance: {member.aiMetrics.workloadBalance}%</div>
                </div>
              </div>
            )}

            {/* Skills */}
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Skills</h4>
              <div className="flex flex-wrap gap-1">
                {member.skills.slice(0, 3).map((skill) => (
                  <span
                    key={skill}
                    className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800"
                  >
                    {skill}
                  </span>
                ))}
                {member.skills.length > 3 && (
                  <span className="text-xs text-gray-500">
                    +{member.skills.length - 3} more
                  </span>
                )}
              </div>
            </div>

            {/* Current Project */}
            {member.currentProject && (
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Briefcase className="w-3 h-3" />
                <span>Working on: {member.currentProject}</span>
              </div>
            )}
          </motion.div>
        ))}
      </div>

      {filteredMembers.length === 0 && (
        <div className="text-center py-12">
          <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No team members found</h3>
          <p className="text-gray-600">Try adjusting your filter criteria</p>
        </div>
      )}

      {/* Optimization Insights */}
      {insights && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Training Recommendations
            </h3>
            <div className="space-y-3">
              {insights.trainingNeeds.map((need, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <Award className="w-5 h-5 text-blue-600 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">{need}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Optimization Suggestions
            </h3>
            <div className="space-y-3">
              {insights.optimizationSuggestions.map((suggestion, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <Brain className="w-5 h-5 text-purple-600 mt-0.5" />
                  <div>
                    <p className="text-sm text-gray-600">{suggestion}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AITeamOptimization;
