'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Brain,
  TrendingUp,
  TrendingDown,
  Package,
  AlertTriangle,
  Target,
  Zap,
  BarChart3,
  Calendar,
  DollarSign,
  Truck,
  Activity,
  Loader2,
  RefreshCw,
  CheckCircle,
  Clock,
  MapPin,
  Settings
} from 'lucide-react';

interface AIInventoryPrediction {
  itemId: string;
  itemName: string;
  currentStock: number;
  predictedDemand: number;
  recommendedOrder: number;
  confidence: number;
  reorderDate: string;
  seasonalTrend: 'up' | 'down' | 'stable';
  costSavings: number;
  riskLevel: 'low' | 'medium' | 'high';
}

interface AIOptimizationMetrics {
  totalSavings: number;
  accuracyRate: number;
  itemsOptimized: number;
  stockoutPrevention: number;
  overStockReduction: number;
  turnoverImprovement: number;
}

const AIInventoryOptimization: React.FC = () => {
  const [predictions, setPredictions] = useState<AIInventoryPrediction[]>([]);
  const [metrics, setMetrics] = useState<AIOptimizationMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [selectedTimeframe, setSelectedTimeframe] = useState('30');

  useEffect(() => {
    fetchAIData();
  }, [selectedTimeframe]);

  const fetchAIData = async () => {
    try {
      setIsLoading(true);
      
      const [predictionsResponse, metricsResponse] = await Promise.all([
        fetch(`/api/inventory/ai/predictions?timeframe=${selectedTimeframe}`),
        fetch('/api/inventory/ai/metrics')
      ]);
      
      const predictionsData = await predictionsResponse.json();
      const metricsData = await metricsResponse.json();
      
      setPredictions(predictionsData.predictions || []);
      setMetrics(metricsData.metrics || null);
      
    } catch (error) {
      console.error('Error fetching AI data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const runOptimization = async () => {
    try {
      setIsOptimizing(true);
      
      const response = await fetch('/api/inventory/ai/optimize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ timeframe: selectedTimeframe }),
      });
      
      if (!response.ok) throw new Error('Optimization failed');
      
      const data = await response.json();
      
      // Refresh data
      await fetchAIData();
      
      // Show success message
      alert(`Optimization completed! Estimated savings: $${data.estimatedSavings}`);
      
    } catch (error) {
      console.error('Error running optimization:', error);
      alert('Failed to run optimization');
    } finally {
      setIsOptimizing(false);
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600 bg-green-100';
    if (confidence >= 0.6) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'text-green-600 bg-green-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'high': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp className="w-4 h-4 text-green-600" />;
      case 'down': return <TrendingDown className="w-4 h-4 text-red-600" />;
      default: return <Activity className="w-4 h-4 text-gray-600" />;
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-12">
        <Loader2 className="w-8 h-8 animate-spin text-blue-600 mr-3" />
        <span className="text-gray-600">Loading AI optimization data...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">AI Inventory Optimization</h2>
          <p className="text-gray-600">
            Machine learning-powered inventory management and demand forecasting
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <select
            value={selectedTimeframe}
            onChange={(e) => setSelectedTimeframe(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="7">7 days</option>
            <option value="30">30 days</option>
            <option value="90">90 days</option>
            <option value="365">1 year</option>
          </select>
          <button
            onClick={fetchAIData}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </button>
          <button
            onClick={runOptimization}
            disabled={isOptimizing}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
          >
            {isOptimizing ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Brain className="w-4 h-4 mr-2" />
            )}
            {isOptimizing ? 'Optimizing...' : 'Run AI Optimization'}
          </button>
        </div>
      </div>

      {/* AI Metrics */}
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-6 text-white"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-100">Total Savings</p>
                <p className="text-3xl font-bold">${metrics.totalSavings.toLocaleString()}</p>
              </div>
              <DollarSign className="w-8 h-8 text-green-200" />
            </div>
            <div className="mt-4 flex items-center">
              <TrendingUp className="w-4 h-4 mr-1" />
              <span className="text-sm">+{metrics.turnoverImprovement}% turnover</span>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-100">Accuracy Rate</p>
                <p className="text-3xl font-bold">{metrics.accuracyRate}%</p>
              </div>
              <Target className="w-8 h-8 text-blue-200" />
            </div>
            <div className="mt-4 flex items-center">
              <Brain className="w-4 h-4 mr-1" />
              <span className="text-sm">Machine Learning</span>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-6 text-white"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-100">Items Optimized</p>
                <p className="text-3xl font-bold">{metrics.itemsOptimized}</p>
              </div>
              <Package className="w-8 h-8 text-purple-200" />
            </div>
            <div className="mt-4 flex items-center">
              <CheckCircle className="w-4 h-4 mr-1" />
              <span className="text-sm">-{metrics.overStockReduction}% overstock</span>
            </div>
          </motion.div>
        </div>
      )}

      {/* AI Predictions Table */}
      <div className="bg-white rounded-lg shadow-sm">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">AI Demand Predictions</h3>
          <p className="text-sm text-gray-600">
            Machine learning predictions for the next {selectedTimeframe} days
          </p>
        </div>
        
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Item
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Current Stock
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Predicted Demand
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Recommended Order
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Confidence
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Trend
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Risk Level
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Savings
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {predictions.map((prediction) => (
                <tr key={prediction.itemId} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {prediction.itemName}
                    </div>
                    <div className="text-sm text-gray-500">
                      Reorder: {new Date(prediction.reorderDate).toLocaleDateString()}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {prediction.currentStock}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {prediction.predictedDemand}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="text-sm font-medium text-blue-600">
                      {prediction.recommendedOrder}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs ${getConfidenceColor(prediction.confidence)}`}>
                      {Math.round(prediction.confidence * 100)}%
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {getTrendIcon(prediction.seasonalTrend)}
                      <span className="ml-1 text-sm text-gray-600 capitalize">
                        {prediction.seasonalTrend}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs ${getRiskColor(prediction.riskLevel)}`}>
                      {prediction.riskLevel.toUpperCase()}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600 font-medium">
                    ${prediction.costSavings.toLocaleString()}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* AI Insights */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Optimization Insights
          </h3>
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <Brain className="w-5 h-5 text-blue-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-900">
                  Seasonal Pattern Detected
                </p>
                <p className="text-sm text-gray-600">
                  HVAC filter demand increases by 35% during spring months
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-900">
                  Stock-out Risk Alert
                </p>
                <p className="text-sm text-gray-600">
                  3 items at high risk of stock-out in next 14 days
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <TrendingUp className="w-5 h-5 text-green-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-900">
                  Efficiency Improvement
                </p>
                <p className="text-sm text-gray-600">
                  AI optimization reduced carrying costs by 18%
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Recommended Actions
          </h3>
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <Truck className="w-5 h-5 text-blue-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-900">
                  Immediate Reorder Required
                </p>
                <p className="text-sm text-gray-600">
                  Order 50 HEPA filters within 3 days to prevent stock-out
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <Calendar className="w-5 h-5 text-purple-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-900">
                  Schedule Bulk Order
                </p>
                <p className="text-sm text-gray-600">
                  Plan compressor order for next month to optimize shipping costs
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <Settings className="w-5 h-5 text-gray-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-900">
                  Adjust Safety Stock
                </p>
                <p className="text-sm text-gray-600">
                  Reduce thermostat safety stock by 20% based on demand patterns
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AIInventoryOptimization;
