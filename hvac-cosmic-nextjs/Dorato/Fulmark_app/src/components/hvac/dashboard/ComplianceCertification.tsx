'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  AlertTriangle,
  Award,
  Calendar,
  CheckCircle2,
  ClipboardCheck,
  FileText,
  Flag,
  MoreVertical,
  Plus,
  Shield,
  XCircle,
} from 'lucide-react';

interface Certification {
  id: string;
  name: string;
  type: 'safety' | 'quality' | 'environmental' | 'technical';
  status: 'active' | 'expired' | 'pending' | 'revoked';
  issueDate: string;
  expiryDate: string;
  issuer: string;
  requirements: string[];
  documents: {
    id: string;
    name: string;
    type: string;
    url: string;
  }[];
}

interface ComplianceRequirement {
  id: string;
  name: string;
  category: 'safety' | 'environmental' | 'quality' | 'legal';
  status: 'compliant' | 'non-compliant' | 'pending' | 'exempt';
  lastChecked: string;
  nextCheck: string;
  description: string;
  regulations: string[];
  evidence: {
    id: string;
    name: string;
    type: string;
    url: string;
  }[];
}

interface Audit {
  id: string;
  name: string;
  type: 'internal' | 'external' | 'regulatory';
  status: 'scheduled' | 'in-progress' | 'completed' | 'failed';
  date: string;
  auditor: string;
  findings: {
    id: string;
    type: 'critical' | 'major' | 'minor' | 'observation';
    description: string;
    status: 'open' | 'closed' | 'in-progress';
    dueDate: string;
  }[];
}

interface ComplianceCertificationProps {
  certifications: Certification[];
  requirements: ComplianceRequirement[];
  audits: Audit[];
}

const ComplianceCertification: React.FC<ComplianceCertificationProps> = ({
  certifications,
  requirements,
  audits,
}) => {
  const [activeTab, setActiveTab] = useState('certifications');
  const [selectedItem, setSelectedItem] = useState<any>(null);

  const tabs = [
    { id: 'certifications', label: 'Certifications', icon: Award },
    { id: 'compliance', label: 'Compliance', icon: Shield },
    { id: 'audits', label: 'Audits', icon: ClipboardCheck },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
      case 'compliant':
      case 'completed':
        return 'text-green-500';
      case 'expired':
      case 'non-compliant':
      case 'failed':
        return 'text-red-500';
      case 'pending':
        return 'text-yellow-500';
      case 'revoked':
        return 'text-gray-500';
      default:
        return 'text-gray-500';
    }
  };

  const getFindingColor = (type: string) => {
    switch (type) {
      case 'critical':
        return 'bg-red-100 text-red-800';
      case 'major':
        return 'bg-orange-100 text-orange-800';
      case 'minor':
        return 'bg-yellow-100 text-yellow-800';
      case 'observation':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const renderCertifications = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-semibold">Certifications</h2>
        <button className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
          <Plus className="w-5 h-5" />
          <span>Add Certification</span>
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {certifications.map((cert) => (
          <motion.div
            key={cert.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-lg shadow p-6 cursor-pointer"
            onClick={() => setSelectedItem(cert)}
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <Award className="w-6 h-6 text-blue-500" />
                <h3 className="text-lg font-semibold">{cert.name}</h3>
              </div>
              <button className="text-gray-400 hover:text-gray-600">
                <MoreVertical className="w-5 h-5" />
              </button>
            </div>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Type</span>
                <span className="text-sm font-medium">{cert.type}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Status</span>
                <span
                  className={`text-sm font-medium ${getStatusColor(
                    cert.status
                  )}`}
                >
                  {cert.status}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Issuer</span>
                <span className="text-sm font-medium">{cert.issuer}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Expiry Date</span>
                <span className="text-sm font-medium">{cert.expiryDate}</span>
              </div>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );

  const renderCompliance = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-semibold">Compliance Requirements</h2>
        <button className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
          <Plus className="w-5 h-5" />
          <span>Add Requirement</span>
        </button>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Requirement
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Category
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Last Check
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Next Check
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {requirements.map((req) => (
              <tr
                key={req.id}
                className="hover:bg-gray-50 cursor-pointer"
                onClick={() => setSelectedItem(req)}
              >
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">
                    {req.name}
                  </div>
                  <div className="text-sm text-gray-500">{req.description}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">
                    {req.category}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span
                    className={`px-2 py-1 text-xs rounded-full ${getStatusColor(
                      req.status
                    )}`}
                  >
                    {req.status}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {req.lastChecked}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {req.nextCheck}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  const renderAudits = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-semibold">Audit History</h2>
        <button className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
          <Plus className="w-5 h-5" />
          <span>Schedule Audit</span>
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {audits.map((audit) => (
          <motion.div
            key={audit.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-lg shadow p-6 cursor-pointer"
            onClick={() => setSelectedItem(audit)}
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <ClipboardCheck className="w-6 h-6 text-blue-500" />
                <h3 className="text-lg font-semibold">{audit.name}</h3>
              </div>
              <span
                className={`px-2 py-1 text-xs rounded-full ${getStatusColor(
                  audit.status
                )}`}
              >
                {audit.status}
              </span>
            </div>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Type</span>
                <span className="text-sm font-medium">{audit.type}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Date</span>
                <span className="text-sm font-medium">{audit.date}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Auditor</span>
                <span className="text-sm font-medium">{audit.auditor}</span>
              </div>
              <div className="mt-4">
                <h4 className="text-sm font-medium mb-2">Findings</h4>
                <div className="space-y-2">
                  {audit.findings.map((finding) => (
                    <div
                      key={finding.id}
                      className="flex items-center justify-between p-2 bg-gray-50 rounded"
                    >
                      <span className="text-sm">{finding.description}</span>
                      <span
                        className={`px-2 py-1 text-xs rounded-full ${getFindingColor(
                          finding.type
                        )}`}
                      >
                        {finding.type}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Tab Navigation */}
      <div className="flex space-x-4 border-b">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`flex items-center space-x-2 px-4 py-2 border-b-2 ${
              activeTab === tab.id
                ? 'border-blue-600 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            <tab.icon className="w-5 h-5" />
            <span>{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {activeTab === 'certifications' && renderCertifications()}
        {activeTab === 'compliance' && renderCompliance()}
        {activeTab === 'audits' && renderAudits()}
      </div>

      {/* Details Modal */}
      {selectedItem && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white rounded-lg shadow-lg p-6 max-w-4xl w-full mx-4"
          >
            <div className="flex justify-between items-start mb-6">
              <div>
                <h2 className="text-xl font-semibold">{selectedItem.name}</h2>
                {selectedItem.description && (
                  <p className="text-sm text-gray-500">
                    {selectedItem.description}
                  </p>
                )}
              </div>
              <button
                onClick={() => setSelectedItem(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                <XCircle className="w-6 h-6" />
              </button>
            </div>

            <div className="space-y-6">
              {/* Certification Details */}
              {selectedItem.type && (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Details</h3>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">Type</span>
                        <span className="text-sm font-medium">
                          {selectedItem.type}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">Status</span>
                        <span
                          className={`text-sm font-medium ${getStatusColor(
                            selectedItem.status
                          )}`}
                        >
                          {selectedItem.status}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">Issue Date</span>
                        <span className="text-sm font-medium">
                          {selectedItem.issueDate}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">Expiry Date</span>
                        <span className="text-sm font-medium">
                          {selectedItem.expiryDate}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Documents</h3>
                    <div className="space-y-2">
                      {selectedItem.documents?.map((doc: any) => (
                        <div
                          key={doc.id}
                          className="flex items-center justify-between p-2 bg-gray-50 rounded"
                        >
                          <span className="text-sm">{doc.name}</span>
                          <a
                            href={doc.url}
                            className="text-blue-600 hover:text-blue-800"
                          >
                            View
                          </a>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {/* Compliance Requirement Details */}
              {selectedItem.category && (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Details</h3>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">Category</span>
                        <span className="text-sm font-medium">
                          {selectedItem.category}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">Status</span>
                        <span
                          className={`text-sm font-medium ${getStatusColor(
                            selectedItem.status
                          )}`}
                        >
                          {selectedItem.status}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">Last Check</span>
                        <span className="text-sm font-medium">
                          {selectedItem.lastChecked}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">Next Check</span>
                        <span className="text-sm font-medium">
                          {selectedItem.nextCheck}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Evidence</h3>
                    <div className="space-y-2">
                      {selectedItem.evidence?.map((item: any) => (
                        <div
                          key={item.id}
                          className="flex items-center justify-between p-2 bg-gray-50 rounded"
                        >
                          <span className="text-sm">{item.name}</span>
                          <a
                            href={item.url}
                            className="text-blue-600 hover:text-blue-800"
                          >
                            View
                          </a>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {/* Audit Details */}
              {selectedItem.findings && (
                <div>
                  <h3 className="text-lg font-semibold mb-2">Findings</h3>
                  <div className="space-y-2">
                    {selectedItem.findings.map((finding: any) => (
                      <div
                        key={finding.id}
                        className="flex items-center justify-between p-2 bg-gray-50 rounded"
                      >
                        <div>
                          <p className="text-sm font-medium">{finding.description}</p>
                          <p className="text-xs text-gray-500">
                            Due: {finding.dueDate}
                          </p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span
                            className={`px-2 py-1 text-xs rounded-full ${getFindingColor(
                              finding.type
                            )}`}
                          >
                            {finding.type}
                          </span>
                          <span
                            className={`px-2 py-1 text-xs rounded-full ${getStatusColor(
                              finding.status
                            )}`}
                          >
                            {finding.status}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
};

export default ComplianceCertification; 