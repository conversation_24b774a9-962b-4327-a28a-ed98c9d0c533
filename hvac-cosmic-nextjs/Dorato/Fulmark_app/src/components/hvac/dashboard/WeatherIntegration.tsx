'use client';

import React from 'react';
import { motion } from 'framer-motion';
import {
  Cloud,
  CloudRain,
  CloudSnow,
  CloudLightning,
  Sun,
  Wind,
  Thermometer,
  Droplets,
  Compass,
  AlertTriangle
} from 'lucide-react';

interface WeatherData {
  current: {
    temperature: number;
    feelsLike: number;
    humidity: number;
    windSpeed: number;
    windDirection: string;
    condition: string;
    icon: string;
  };
  forecast: Array<{
    date: string;
    temperature: {
      min: number;
      max: number;
    };
    condition: string;
    icon: string;
    precipitation: number;
    windSpeed: number;
  }>;
  alerts: Array<{
    type: 'warning' | 'alert' | 'info';
    message: string;
    severity: 'low' | 'medium' | 'high';
  }>;
  hvacRecommendations: Array<{
    type: 'cooling' | 'heating' | 'ventilation';
    recommendation: string;
    impact: 'low' | 'medium' | 'high';
  }>;
}

interface WeatherIntegrationProps {
  data: WeatherData;
}

const WeatherIntegration: React.FC<WeatherIntegrationProps> = ({ data }) => {
  const getWeatherIcon = (condition: string) => {
    switch (condition.toLowerCase()) {
      case 'sunny':
        return <Sun className="w-8 h-8 text-yellow-500" />;
      case 'cloudy':
        return <Cloud className="w-8 h-8 text-gray-500" />;
      case 'rain':
        return <CloudRain className="w-8 h-8 text-blue-500" />;
      case 'snow':
        return <CloudSnow className="w-8 h-8 text-blue-300" />;
      case 'thunderstorm':
        return <CloudLightning className="w-8 h-8 text-purple-500" />;
      default:
        return <Cloud className="w-8 h-8 text-gray-500" />;
    }
  };

  const getAlertColor = (severity: string) => {
    switch (severity) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high':
        return 'border-red-500';
      case 'medium':
        return 'border-yellow-500';
      case 'low':
        return 'border-green-500';
      default:
        return 'border-gray-500';
    }
  };

  return (
    <div className="space-y-6">
      {/* Current Weather */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-lg shadow-sm p-6"
      >
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            {getWeatherIcon(data.current.condition)}
            <div>
              <h3 className="text-2xl font-bold text-gray-900">
                {data.current.temperature}°C
              </h3>
              <p className="text-gray-600">{data.current.condition}</p>
            </div>
          </div>
          <div className="text-right">
            <p className="text-sm text-gray-600">
              Odczuwalna: {data.current.feelsLike}°C
            </p>
            <p className="text-sm text-gray-600">
              Wilgotność: {data.current.humidity}%
            </p>
          </div>
        </div>

        <div className="grid grid-cols-3 gap-4">
          <div className="flex items-center space-x-2">
            <Wind className="w-5 h-5 text-blue-500" />
            <div>
              <p className="text-sm font-medium text-gray-900">
                {data.current.windSpeed} km/h
              </p>
              <p className="text-xs text-gray-600">Prędkość wiatru</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Compass className="w-5 h-5 text-blue-500" />
            <div>
              <p className="text-sm font-medium text-gray-900">
                {data.current.windDirection}
              </p>
              <p className="text-xs text-gray-600">Kierunek wiatru</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Droplets className="w-5 h-5 text-blue-500" />
            <div>
              <p className="text-sm font-medium text-gray-900">
                {data.forecast[0].precipitation}%
              </p>
              <p className="text-xs text-gray-600">Szansa opadów</p>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Weather Forecast */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="bg-white rounded-lg shadow-sm p-6"
      >
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Prognoza Pogody</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {data.forecast.map((day, index) => (
            <div
              key={index}
              className="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
            >
              <div className="flex items-center space-x-3">
                {getWeatherIcon(day.condition)}
                <div>
                  <p className="font-medium text-gray-900">
                    {new Date(day.date).toLocaleDateString('pl-PL', { weekday: 'short' })}
                  </p>
                  <p className="text-sm text-gray-600">{day.condition}</p>
                </div>
              </div>
              <div className="text-right">
                <p className="font-medium text-gray-900">
                  {day.temperature.max}°C
                </p>
                <p className="text-sm text-gray-600">
                  {day.temperature.min}°C
                </p>
              </div>
            </div>
          ))}
        </div>
      </motion.div>

      {/* Weather Alerts */}
      {data.alerts.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-lg shadow-sm p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Alerty Pogodowe</h3>
          <div className="space-y-3">
            {data.alerts.map((alert, index) => (
              <div
                key={index}
                className={`flex items-start space-x-3 p-3 rounded-lg ${getAlertColor(alert.severity)}`}
              >
                <AlertTriangle className="w-5 h-5 flex-shrink-0" />
                <p className="text-sm">{alert.message}</p>
              </div>
            ))}
          </div>
        </motion.div>
      )}

      {/* HVAC Recommendations */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="bg-white rounded-lg shadow-sm p-6"
      >
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Rekomendacje HVAC</h3>
        <div className="space-y-4">
          {data.hvacRecommendations.map((rec, index) => (
            <div
              key={index}
              className={`p-4 border-l-4 ${getImpactColor(rec.impact)} bg-gray-50 rounded-r-lg`}
            >
              <div className="flex items-center justify-between mb-2">
                <span className="font-medium text-gray-900">
                  {rec.type === 'cooling' ? 'Chłodzenie' :
                   rec.type === 'heating' ? 'Ogrzewanie' : 'Wentylacja'}
                </span>
                <span className={`text-sm ${
                  rec.impact === 'high' ? 'text-red-600' :
                  rec.impact === 'medium' ? 'text-yellow-600' :
                  'text-green-600'
                }`}>
                  {rec.impact === 'high' ? 'Wysoki' :
                   rec.impact === 'medium' ? 'Średni' : 'Niski'} wpływ
                </span>
              </div>
              <p className="text-sm text-gray-600">{rec.recommendation}</p>
            </div>
          ))}
        </div>
      </motion.div>
    </div>
  );
};

export default WeatherIntegration; 