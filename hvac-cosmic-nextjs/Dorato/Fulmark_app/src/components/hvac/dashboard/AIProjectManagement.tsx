'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Calendar,
  Clock,
  Users,
  CheckCircle2,
  AlertTriangle,
  XCircle,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Target,
  Brain,
  Zap,
  BarChart3,
  Activity,
  Loader2,
  RefreshCw,
  Plus,
  Filter,
  Search,
  MapPin,
  FileText,
  Settings,
  Truck,
  Wrench
} from 'lucide-react';

interface AIProjectInsights {
  riskLevel: 'low' | 'medium' | 'high';
  completionPrediction: string;
  budgetForecast: number;
  delayProbability: number;
  resourceOptimization: string[];
  recommendations: string[];
  confidence: number;
}

interface Project {
  id: string;
  name: string;
  client: string;
  status: 'planning' | 'in-progress' | 'completed' | 'on-hold' | 'delayed';
  progress: number;
  startDate: string;
  endDate: string;
  budget: number;
  spent: number;
  category: 'installation' | 'maintenance' | 'repair' | 'consultation';
  location: string;
  team: Array<{
    id: string;
    name: string;
    role: string;
    avatar?: string;
  }>;
  aiInsights?: AIProjectInsights;
}

interface ProjectMetrics {
  totalProjects: number;
  activeProjects: number;
  completedProjects: number;
  totalBudget: number;
  totalSpent: number;
  onTimeDelivery: number;
  averageProjectDuration: number;
  teamUtilization: number;
  aiAccuracy: number;
  riskMitigation: number;
}

const AIProjectManagement: React.FC = () => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [metrics, setMetrics] = useState<ProjectMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  useEffect(() => {
    fetchProjects();
  }, []);

  const fetchProjects = async () => {
    try {
      setIsLoading(true);
      
      const [projectsResponse, metricsResponse] = await Promise.all([
        fetch('/api/projects/ai'),
        fetch('/api/projects/ai/metrics')
      ]);
      
      const projectsData = await projectsResponse.json();
      const metricsData = await metricsResponse.json();
      
      setProjects(projectsData.projects || []);
      setMetrics(metricsData.metrics || null);
      
    } catch (error) {
      console.error('Error fetching AI projects:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const runAIAnalysis = async () => {
    try {
      setIsAnalyzing(true);
      
      const response = await fetch('/api/projects/ai/analyze', {
        method: 'POST',
      });
      
      if (!response.ok) throw new Error('AI analysis failed');
      
      const data = await response.json();
      
      // Refresh data
      await fetchProjects();
      
      alert(`AI Analysis completed! ${data.projectsAnalyzed} projects analyzed with ${data.averageAccuracy}% accuracy`);
      
    } catch (error) {
      console.error('Error running AI analysis:', error);
      alert('Failed to run AI analysis');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-100';
      case 'in-progress':
        return 'text-blue-600 bg-blue-100';
      case 'planning':
        return 'text-yellow-600 bg-yellow-100';
      case 'on-hold':
        return 'text-gray-600 bg-gray-100';
      case 'delayed':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'text-green-600 bg-green-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'high': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'installation': return <Settings className="w-4 h-4" />;
      case 'maintenance': return <Wrench className="w-4 h-4" />;
      case 'repair': return <AlertTriangle className="w-4 h-4" />;
      case 'consultation': return <FileText className="w-4 h-4" />;
      default: return <Target className="w-4 h-4" />;
    }
  };

  const getProgressColor = (progress: number) => {
    if (progress >= 80) return 'bg-green-500';
    if (progress >= 50) return 'bg-blue-500';
    if (progress >= 25) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const filteredProjects = projects.filter(project => {
    const matchesSearch = project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.client.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || project.status === statusFilter;
    const matchesCategory = categoryFilter === 'all' || project.category === categoryFilter;
    
    return matchesSearch && matchesStatus && matchesCategory;
  });

  const statuses = ['all', 'planning', 'in-progress', 'completed', 'on-hold', 'delayed'];
  const categories = ['all', 'installation', 'maintenance', 'repair', 'consultation'];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-12">
        <Loader2 className="w-8 h-8 animate-spin text-blue-600 mr-3" />
        <span className="text-gray-600">Loading AI project management...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">AI Project Management</h2>
          <p className="text-gray-600">
            Intelligent project tracking with predictive analytics and risk assessment
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={fetchProjects}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </button>
          <button
            onClick={runAIAnalysis}
            disabled={isAnalyzing}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
          >
            {isAnalyzing ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Brain className="w-4 h-4 mr-2" />
            )}
            {isAnalyzing ? 'Analyzing...' : 'Run AI Analysis'}
          </button>
        </div>
      </div>

      {/* AI Metrics */}
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-100">Active Projects</p>
                <p className="text-3xl font-bold">{metrics.activeProjects}</p>
              </div>
              <Target className="w-8 h-8 text-blue-200" />
            </div>
            <div className="mt-4 flex items-center">
              <TrendingUp className="w-4 h-4 mr-1" />
              <span className="text-sm">+{metrics.teamUtilization}% utilization</span>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-6 text-white"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-100">On-Time Delivery</p>
                <p className="text-3xl font-bold">{metrics.onTimeDelivery}%</p>
              </div>
              <CheckCircle2 className="w-8 h-8 text-green-200" />
            </div>
            <div className="mt-4 flex items-center">
              <Brain className="w-4 h-4 mr-1" />
              <span className="text-sm">AI Optimized</span>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-6 text-white"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-100">AI Accuracy</p>
                <p className="text-3xl font-bold">{metrics.aiAccuracy}%</p>
              </div>
              <Brain className="w-8 h-8 text-purple-200" />
            </div>
            <div className="mt-4 flex items-center">
              <Zap className="w-4 h-4 mr-1" />
              <span className="text-sm">Machine Learning</span>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg p-6 text-white"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-100">Risk Mitigation</p>
                <p className="text-3xl font-bold">{metrics.riskMitigation}%</p>
              </div>
              <AlertTriangle className="w-8 h-8 text-orange-200" />
            </div>
            <div className="mt-4 flex items-center">
              <Activity className="w-4 h-4 mr-1" />
              <span className="text-sm">Predictive Analytics</span>
            </div>
          </motion.div>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Search Projects
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search by name or client..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Status
            </label>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {statuses.map(status => (
                <option key={status} value={status}>
                  {status === 'all' ? 'All Statuses' : status.charAt(0).toUpperCase() + status.slice(1)}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Category
            </label>
            <select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {categories.map(category => (
                <option key={category} value={category}>
                  {category === 'all' ? 'All Categories' : category.charAt(0).toUpperCase() + category.slice(1)}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Projects Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredProjects.map((project) => (
          <motion.div
            key={project.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer"
            onClick={() => setSelectedProject(project)}
          >
            {/* Project Header */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-3">
                {getCategoryIcon(project.category)}
                <div>
                  <h3 className="font-semibold text-gray-900">{project.name}</h3>
                  <p className="text-sm text-gray-600">{project.client}</p>
                </div>
              </div>
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs ${getStatusColor(project.status)}`}>
                {project.status.replace('-', ' ').toUpperCase()}
              </span>
            </div>

            {/* Progress */}
            <div className="mb-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-gray-600">Progress</span>
                <span className="text-sm font-medium">{project.progress}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full ${getProgressColor(project.progress)}`}
                  style={{ width: `${project.progress}%` }}
                />
              </div>
            </div>

            {/* AI Insights */}
            {project.aiInsights && (
              <div className="mb-4 p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700">AI Insights</span>
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs ${getRiskColor(project.aiInsights.riskLevel)}`}>
                    {project.aiInsights.riskLevel.toUpperCase()} RISK
                  </span>
                </div>
                <div className="space-y-1 text-xs text-gray-600">
                  <div>Completion: {project.aiInsights.completionPrediction}</div>
                  <div>Budget forecast: ${project.aiInsights.budgetForecast.toLocaleString()}</div>
                  <div>Confidence: {Math.round(project.aiInsights.confidence * 100)}%</div>
                </div>
              </div>
            )}

            {/* Project Details */}
            <div className="space-y-2 text-sm text-gray-600">
              <div className="flex items-center justify-between">
                <span>Budget:</span>
                <span className="font-medium">${project.budget.toLocaleString()}</span>
              </div>
              <div className="flex items-center justify-between">
                <span>Spent:</span>
                <span className="font-medium">${project.spent.toLocaleString()}</span>
              </div>
              <div className="flex items-center space-x-2">
                <MapPin className="w-3 h-3" />
                <span>{project.location}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Calendar className="w-3 h-3" />
                <span>{project.startDate} - {project.endDate}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Users className="w-3 h-3" />
                <span>{project.team.length} team members</span>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {filteredProjects.length === 0 && (
        <div className="text-center py-12">
          <Target className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No projects found</h3>
          <p className="text-gray-600">Try adjusting your search or filter criteria</p>
        </div>
      )}
    </div>
  );
};

export default AIProjectManagement;
