'use client';

// 👨‍🔧 Technician Dashboard - PEŁNA MOC WIATRU! ⚡

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Users, 
  MapPin, 
  Clock, 
  Star, 
  Activity,
  Wrench,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Navigation,
  Phone,
  Mail
} from 'lucide-react';

interface Technician {
  id: number;
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  status: 'available' | 'busy' | 'on_route' | 'offline' | 'break';
  specializations: string[];
  averageRating: number;
  completedJobs: number;
  currentLatitude?: number;
  currentLongitude?: number;
  activeTickets: number;
}

const TechnicianDashboard: React.FC = () => {
  const [technicians, setTechnicians] = useState<Technician[]>([]);
  const [loading, setLoading] = useState(true);

  // Mock data
  useEffect(() => {
    setTimeout(() => {
      setTechnicians([
        {
          id: 1,
          firstName: '<PERSON><PERSON>',
          lastName: '<PERSON><PERSON><PERSON>',
          email: 'mare<PERSON>.kowa<PERSON><PERSON>@fulmark.com',
          phone: '+48 123 456 789',
          status: 'busy',
          specializations: ['Klimatyzacja', '<PERSON><PERSON><PERSON><PERSON><PERSON>'],
          averageRating: 4.8,
          completedJobs: 156,
          currentLatitude: 52.2297,
          currentLongitude: 21.0122,
          activeTickets: 2,
        },
        {
          id: 2,
          firstName: 'Anna',
          lastName: 'Nowak',
          email: '<EMAIL>',
          phone: '+48 987 654 321',
          status: 'available',
          specializations: ['Ogrzewanie', 'Pompy ciepła'],
          averageRating: 4.9,
          completedJobs: 203,
          currentLatitude: 52.4064,
          currentLongitude: 16.9252,
          activeTickets: 0,
        },
        {
          id: 3,
          firstName: 'Piotr',
          lastName: 'Wiśniewski',
          email: '<EMAIL>',
          phone: '+48 555 123 456',
          status: 'on_route',
          specializations: ['Klimatyzacja', 'Serwis'],
          averageRating: 4.7,
          completedJobs: 89,
          currentLatitude: 50.0647,
          currentLongitude: 19.9450,
          activeTickets: 1,
        },
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'bg-green-100 text-green-800 border-green-200';
      case 'busy': return 'bg-red-100 text-red-800 border-red-200';
      case 'on_route': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'break': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'offline': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'available': return 'Dostępny';
      case 'busy': return 'Zajęty';
      case 'on_route': return 'W drodze';
      case 'break': return 'Przerwa';
      case 'offline': return 'Offline';
      default: return status;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'available': return <CheckCircle className="w-4 h-4" />;
      case 'busy': return <AlertCircle className="w-4 h-4" />;
      case 'on_route': return <Navigation className="w-4 h-4" />;
      case 'break': return <Clock className="w-4 h-4" />;
      case 'offline': return <Activity className="w-4 h-4" />;
      default: return <Activity className="w-4 h-4" />;
    }
  };

  const getWorkloadLevel = (activeTickets: number) => {
    if (activeTickets === 0) return { level: 'Brak', color: 'text-green-600' };
    if (activeTickets <= 2) return { level: 'Normalny', color: 'text-blue-600' };
    if (activeTickets <= 4) return { level: 'Wysoki', color: 'text-orange-600' };
    return { level: 'Przeciążony', color: 'text-red-600' };
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full"
        />
      </div>
    );
  }

  const availableTechnicians = technicians.filter(t => t.status === 'available').length;
  const busyTechnicians = technicians.filter(t => t.status === 'busy').length;
  const averageRating = technicians.reduce((sum, t) => sum + t.averageRating, 0) / technicians.length;
  const totalJobs = technicians.reduce((sum, t) => sum + t.completedJobs, 0);

  return (
    <div className="p-6 space-y-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-lg shadow-sm p-6"
      >
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Panel Techników</h1>
            <p className="text-gray-600 mt-1">Zarządzanie zespołem techników z real-time tracking</p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="text-right">
              <p className="text-sm text-gray-500">Dostępnych</p>
              <p className="text-2xl font-bold text-green-600">{availableTechnicians}</p>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-500">Zajętych</p>
              <p className="text-2xl font-bold text-red-600">{busyTechnicians}</p>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-lg shadow-sm p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Wszyscy Technicy</p>
              <p className="text-2xl font-bold text-gray-900">{technicians.length}</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-full">
              <Users className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-lg shadow-sm p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Średnia Ocena</p>
              <p className="text-2xl font-bold text-gray-900">{averageRating.toFixed(1)}</p>
            </div>
            <div className="p-3 bg-yellow-100 rounded-full">
              <Star className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.3 }}
          className="bg-white rounded-lg shadow-sm p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Ukończone Zlecenia</p>
              <p className="text-2xl font-bold text-gray-900">{totalJobs}</p>
            </div>
            <div className="p-3 bg-green-100 rounded-full">
              <Wrench className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.4 }}
          className="bg-white rounded-lg shadow-sm p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Efektywność</p>
              <p className="text-2xl font-bold text-gray-900">94%</p>
            </div>
            <div className="p-3 bg-purple-100 rounded-full">
              <TrendingUp className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </motion.div>
      </div>

      {/* Technicians Grid */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
      >
        {technicians.map((technician) => {
          const workload = getWorkloadLevel(technician.activeTickets);
          
          return (
            <motion.div
              key={technician.id}
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              className="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow"
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                    {technician.firstName[0]}{technician.lastName[0]}
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">
                      {technician.firstName} {technician.lastName}
                    </h3>
                    <div className="flex items-center space-x-1 mt-1">
                      <Star className="w-4 h-4 text-yellow-500" />
                      <span className="text-sm text-gray-600">{technician.averageRating}</span>
                      <span className="text-sm text-gray-400">({technician.completedJobs} zleceń)</span>
                    </div>
                  </div>
                </div>
                
                <div className={`flex items-center space-x-1 px-2 py-1 rounded-full border text-xs font-medium ${getStatusColor(technician.status)}`}>
                  {getStatusIcon(technician.status)}
                  <span>{getStatusLabel(technician.status)}</span>
                </div>
              </div>

              <div className="space-y-3">
                <div>
                  <p className="text-sm font-medium text-gray-700 mb-1">Specjalizacje</p>
                  <div className="flex flex-wrap gap-1">
                    {technician.specializations.map((spec, index) => (
                      <span key={index} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                        {spec}
                      </span>
                    ))}
                  </div>
                </div>

                <div className="flex justify-between items-center">
                  <div>
                    <p className="text-sm text-gray-600">Obciążenie</p>
                    <p className={`text-sm font-medium ${workload.color}`}>
                      {workload.level} ({technician.activeTickets} aktywnych)
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-600">Lokalizacja</p>
                    <div className="flex items-center text-sm text-gray-500">
                      <MapPin className="w-3 h-3 mr-1" />
                      {technician.currentLatitude ? 'Aktywna' : 'Nieznana'}
                    </div>
                  </div>
                </div>

                <div className="flex space-x-2 pt-3 border-t">
                  <button className="flex-1 flex items-center justify-center space-x-1 px-3 py-2 bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 transition-colors">
                    <Phone className="w-4 h-4" />
                    <span className="text-sm">Zadzwoń</span>
                  </button>
                  <button className="flex-1 flex items-center justify-center space-x-1 px-3 py-2 bg-gray-50 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors">
                    <Mail className="w-4 h-4" />
                    <span className="text-sm">Email</span>
                  </button>
                  <button className="flex-1 flex items-center justify-center space-x-1 px-3 py-2 bg-green-50 text-green-700 rounded-lg hover:bg-green-100 transition-colors">
                    <MapPin className="w-4 h-4" />
                    <span className="text-sm">Mapa</span>
                  </button>
                </div>
              </div>
            </motion.div>
          );
        })}
      </motion.div>
    </div>
  );
};

export default TechnicianDashboard;