// 👨‍🔧 Technician Service - PEŁNA MOC WIATRU! ⚡
// Complete technician management with location tracking and performance analytics

import { eq, and, desc, asc, like, sql, inArray } from 'drizzle-orm';
import { db } from '@/libs/DB';
import { 
  technicians, 
  serviceTickets,
  companies,
  type technicianStatusEnum 
} from '@/models/Schema';

export interface CreateTechnicianData {
  companyId: number;
  employeeId?: string;
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  specializations?: string[];
  certifications?: string[];
  hourlyRate?: number;
  workingHours?: {
    monday: { start: string; end: string; available: boolean };
    tuesday: { start: string; end: string; available: boolean };
    wednesday: { start: string; end: string; available: boolean };
    thursday: { start: string; end: string; available: boolean };
    friday: { start: string; end: string; available: boolean };
    saturday: { start: string; end: string; available: boolean };
    sunday: { start: string; end: string; available: boolean };
  };
  hireDate?: Date;
}

export interface UpdateTechnicianData extends Partial<CreateTechnicianData> {
  status?: typeof technicianStatusEnum.enumValues[number];
  currentLatitude?: number;
  currentLongitude?: number;
  averageRating?: number;
  completedJobs?: number;
}

export interface TechnicianFilters {
  companyId: number;
  status?: typeof technicianStatusEnum.enumValues[number][];
  specialization?: string;
  isActive?: boolean;
  search?: string;
  availableNow?: boolean;
  nearLocation?: {
    latitude: number;
    longitude: number;
    radiusKm: number;
  };
}

export interface TechnicianPerformance {
  technicianId: number;
  name: string;
  completedJobs: number;
  averageRating: number;
  averageCompletionTime: number; // hours
  customerSatisfaction: number;
  onTimePercentage: number;
  revenue: number;
  efficiency: number; // jobs per day
}

export interface LocationUpdate {
  technicianId: number;
  latitude: number;
  longitude: number;
  timestamp?: Date;
}

export class TechnicianService {
  
  // Create new technician
  async createTechnician(data: CreateTechnicianData) {
    try {
      const [technician] = await db.insert(technicians).values({
        ...data,
        specializations: data.specializations ? JSON.stringify(data.specializations) : null,
        certifications: data.certifications ? JSON.stringify(data.certifications) : null,
        workingHours: data.workingHours ? JSON.stringify(data.workingHours) : null,
        averageRating: 5.0,
        completedJobs: 0,
      }).returning();

      return technician;
    } catch (error) {
      console.error('Error creating technician:', error);
      throw new Error('Failed to create technician');
    }
  }

  // Get technician by ID
  async getTechnicianById(id: number, companyId: number) {
    try {
      const technician = await db.query.technicians.findFirst({
        where: and(eq(technicians.id, id), eq(technicians.companyId, companyId)),
        with: {
          serviceTickets: {
            orderBy: desc(serviceTickets.createdAt),
            limit: 10,
            with: {
              customer: true,
            }
          }
        }
      });

      if (!technician) {
        throw new Error('Technician not found');
      }

      return technician;
    } catch (error) {
      console.error('Error fetching technician:', error);
      throw new Error('Failed to fetch technician');
    }
  }

  // Get technicians with filters
  async getTechnicians(filters: TechnicianFilters, page = 1, limit = 20) {
    try {
      const offset = (page - 1) * limit;
      const conditions = [eq(technicians.companyId, filters.companyId)];

      // Apply filters
      if (filters.status && filters.status.length > 0) {
        conditions.push(inArray(technicians.status, filters.status));
      }

      if (filters.specialization) {
        conditions.push(
          sql`${technicians.specializations}::text ILIKE ${`%${filters.specialization}%`}`
        );
      }

      if (filters.isActive !== undefined) {
        conditions.push(eq(technicians.isActive, filters.isActive));
      }

      if (filters.search) {
        conditions.push(
          sql`(${technicians.firstName} ILIKE ${`%${filters.search}%`} OR 
               ${technicians.lastName} ILIKE ${`%${filters.search}%`} OR 
               ${technicians.email} ILIKE ${`%${filters.search}%`} OR 
               ${technicians.employeeId} ILIKE ${`%${filters.search}%`})`
        );
      }

      if (filters.availableNow) {
        conditions.push(inArray(technicians.status, ['available', 'break']));
      }

      const whereClause = conditions.length > 1 ? and(...conditions) : conditions[0];

      // Get technicians with pagination
      let techniciansList = await db.query.technicians.findMany({
        where: whereClause,
        orderBy: [asc(technicians.status), desc(technicians.averageRating)],
        limit,
        offset,
      });

      // Apply location filter if specified
      if (filters.nearLocation) {
        techniciansList = techniciansList.filter(tech => {
          if (!tech.currentLatitude || !tech.currentLongitude) return false;
          
          const distance = this.calculateDistance(
            filters.nearLocation!.latitude,
            filters.nearLocation!.longitude,
            Number(tech.currentLatitude),
            Number(tech.currentLongitude)
          );
          
          return distance <= filters.nearLocation!.radiusKm;
        });
      }

      // Get total count
      const [{ count }] = await db
        .select({ count: sql<number>`count(*)` })
        .from(technicians)
        .where(whereClause);

      return {
        technicians: techniciansList,
        total: count,
        page,
        limit,
        totalPages: Math.ceil(count / limit),
      };
    } catch (error) {
      console.error('Error fetching technicians:', error);
      throw new Error('Failed to fetch technicians');
    }
  }

  // Update technician
  async updateTechnician(id: number, companyId: number, data: UpdateTechnicianData) {
    try {
      const [technician] = await db
        .update(technicians)
        .set({
          ...data,
          specializations: data.specializations ? JSON.stringify(data.specializations) : undefined,
          certifications: data.certifications ? JSON.stringify(data.certifications) : undefined,
          workingHours: data.workingHours ? JSON.stringify(data.workingHours) : undefined,
          updatedAt: new Date(),
        })
        .where(and(eq(technicians.id, id), eq(technicians.companyId, companyId)))
        .returning();

      if (!technician) {
        throw new Error('Technician not found');
      }

      return technician;
    } catch (error) {
      console.error('Error updating technician:', error);
      throw new Error('Failed to update technician');
    }
  }

  // Update technician location
  async updateLocation(data: LocationUpdate) {
    try {
      const [technician] = await db
        .update(technicians)
        .set({
          currentLatitude: data.latitude.toString(),
          currentLongitude: data.longitude.toString(),
          lastLocationUpdate: data.timestamp || new Date(),
          updatedAt: new Date(),
        })
        .where(eq(technicians.id, data.technicianId))
        .returning();

      if (!technician) {
        throw new Error('Technician not found');
      }

      return technician;
    } catch (error) {
      console.error('Error updating technician location:', error);
      throw new Error('Failed to update technician location');
    }
  }

  // Update technician status
  async updateStatus(id: number, companyId: number, status: typeof technicianStatusEnum.enumValues[number]) {
    try {
      return await this.updateTechnician(id, companyId, { status });
    } catch (error) {
      console.error('Error updating technician status:', error);
      throw new Error('Failed to update technician status');
    }
  }

  // Get available technicians for assignment
  async getAvailableTechnicians(companyId: number, specialization?: string, location?: { latitude: number; longitude: number }) {
    try {
      const filters: TechnicianFilters = {
        companyId,
        status: ['available', 'break'],
        isActive: true,
        specialization,
      };

      if (location) {
        filters.nearLocation = {
          latitude: location.latitude,
          longitude: location.longitude,
          radiusKm: 50, // 50km radius
        };
      }

      const result = await this.getTechnicians(filters, 1, 50);
      return result.technicians;
    } catch (error) {
      console.error('Error fetching available technicians:', error);
      throw new Error('Failed to fetch available technicians');
    }
  }

  // Get technician performance metrics
  async getTechnicianPerformance(companyId: number, dateFrom?: Date, dateTo?: Date): Promise<TechnicianPerformance[]> {
    try {
      const dateConditions = [];
      if (dateFrom) {
        dateConditions.push(sql`${serviceTickets.createdAt} >= ${dateFrom}`);
      }
      if (dateTo) {
        dateConditions.push(sql`${serviceTickets.createdAt} <= ${dateTo}`);
      }

      const performanceData = await db
        .select({
          technicianId: technicians.id,
          firstName: technicians.firstName,
          lastName: technicians.lastName,
          completedJobs: sql<number>`count(${serviceTickets.id})`,
          averageRating: sql<number>`avg(${serviceTickets.customerRating})`,
          averageCompletionTime: sql<number>`
            avg(EXTRACT(EPOCH FROM (${serviceTickets.actualEndTime} - ${serviceTickets.actualStartTime})) / 3600)
          `,
          totalRevenue: sql<number>`sum(${serviceTickets.actualCost})`,
        })
        .from(technicians)
        .leftJoin(serviceTickets, and(
          eq(serviceTickets.assignedTechnicianId, technicians.id),
          eq(serviceTickets.status, 'completed'),
          ...dateConditions
        ))
        .where(and(
          eq(technicians.companyId, companyId),
          eq(technicians.isActive, true)
        ))
        .groupBy(technicians.id, technicians.firstName, technicians.lastName);

      return performanceData.map(data => ({
        technicianId: data.technicianId,
        name: `${data.firstName} ${data.lastName}`,
        completedJobs: data.completedJobs,
        averageRating: Number(data.averageRating || 0),
        averageCompletionTime: Number(data.averageCompletionTime || 0),
        customerSatisfaction: Number(data.averageRating || 0) * 20, // Convert 1-5 to 0-100
        onTimePercentage: 85, // Mock data - would need actual calculation
        revenue: Number(data.totalRevenue || 0),
        efficiency: data.completedJobs / 30, // Jobs per month / 30 days
      }));
    } catch (error) {
      console.error('Error fetching technician performance:', error);
      throw new Error('Failed to fetch technician performance');
    }
  }

  // Get technician schedule
  async getTechnicianSchedule(technicianId: number, companyId: number, dateFrom: Date, dateTo: Date) {
    try {
      const schedule = await db.query.serviceTickets.findMany({
        where: and(
          eq(serviceTickets.assignedTechnicianId, technicianId),
          eq(serviceTickets.companyId, companyId),
          sql`${serviceTickets.scheduledDate} >= ${dateFrom}`,
          sql`${serviceTickets.scheduledDate} <= ${dateTo}`,
          inArray(serviceTickets.status, ['assigned', 'in_progress', 'completed'])
        ),
        orderBy: asc(serviceTickets.scheduledDate),
        with: {
          customer: true,
          building: true,
        }
      });

      return schedule;
    } catch (error) {
      console.error('Error fetching technician schedule:', error);
      throw new Error('Failed to fetch technician schedule');
    }
  }

  // Calculate route optimization for technician
  async optimizeRoute(technicianId: number, companyId: number, date: Date) {
    try {
      const technician = await this.getTechnicianById(technicianId, companyId);
      const schedule = await this.getTechnicianSchedule(technicianId, companyId, date, date);

      // Filter tickets with location data
      const ticketsWithLocation = schedule.filter(ticket => 
        ticket.building?.latitude && ticket.building?.longitude
      );

      if (ticketsWithLocation.length === 0) {
        return { optimizedRoute: [], totalDistance: 0, estimatedTime: 0 };
      }

      // Simple route optimization (in production, use Google Maps API or similar)
      const startLocation = {
        latitude: Number(technician.currentLatitude) || 52.2297, // Warsaw default
        longitude: Number(technician.currentLongitude) || 21.0122,
      };

      const optimizedRoute = this.calculateOptimalRoute(startLocation, ticketsWithLocation);
      
      return optimizedRoute;
    } catch (error) {
      console.error('Error optimizing route:', error);
      throw new Error('Failed to optimize route');
    }
  }

  // Simple route optimization algorithm
  private calculateOptimalRoute(startLocation: { latitude: number; longitude: number }, tickets: any[]) {
    const unvisited = [...tickets];
    const route = [];
    let currentLocation = startLocation;
    let totalDistance = 0;

    while (unvisited.length > 0) {
      let nearestIndex = 0;
      let nearestDistance = Infinity;

      // Find nearest unvisited ticket
      unvisited.forEach((ticket, index) => {
        const distance = this.calculateDistance(
          currentLocation.latitude,
          currentLocation.longitude,
          Number(ticket.building.latitude),
          Number(ticket.building.longitude)
        );

        if (distance < nearestDistance) {
          nearestDistance = distance;
          nearestIndex = index;
        }
      });

      // Add nearest ticket to route
      const nearestTicket = unvisited.splice(nearestIndex, 1)[0];
      route.push(nearestTicket);
      totalDistance += nearestDistance;

      // Update current location
      currentLocation = {
        latitude: Number(nearestTicket.building.latitude),
        longitude: Number(nearestTicket.building.longitude),
      };
    }

    return {
      optimizedRoute: route,
      totalDistance: Math.round(totalDistance * 100) / 100,
      estimatedTime: Math.round(totalDistance * 2), // Rough estimate: 2 minutes per km
    };
  }

  // Calculate distance between two coordinates (Haversine formula)
  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) * 
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI/180);
  }

  // Get technician workload
  async getTechnicianWorkload(companyId: number) {
    try {
      const workloadData = await db
        .select({
          technicianId: technicians.id,
          firstName: technicians.firstName,
          lastName: technicians.lastName,
          status: technicians.status,
          activeTickets: sql<number>`count(${serviceTickets.id})`,
          avgRating: technicians.averageRating,
        })
        .from(technicians)
        .leftJoin(serviceTickets, and(
          eq(serviceTickets.assignedTechnicianId, technicians.id),
          inArray(serviceTickets.status, ['assigned', 'in_progress'])
        ))
        .where(and(
          eq(technicians.companyId, companyId),
          eq(technicians.isActive, true)
        ))
        .groupBy(technicians.id, technicians.firstName, technicians.lastName, 
                 technicians.status, technicians.averageRating);

      return workloadData.map(data => ({
        technicianId: data.technicianId,
        name: `${data.firstName} ${data.lastName}`,
        status: data.status,
        activeTickets: data.activeTickets,
        averageRating: Number(data.avgRating),
        workloadLevel: this.calculateWorkloadLevel(data.activeTickets),
      }));
    } catch (error) {
      console.error('Error fetching technician workload:', error);
      throw new Error('Failed to fetch technician workload');
    }
  }

  // Calculate workload level
  private calculateWorkloadLevel(activeTickets: number): 'low' | 'medium' | 'high' | 'overloaded' {
    if (activeTickets === 0) return 'low';
    if (activeTickets <= 2) return 'medium';
    if (activeTickets <= 4) return 'high';
    return 'overloaded';
  }

  // Check technician availability
  async checkAvailability(technicianId: number, companyId: number, date: Date, duration: number) {
    try {
      const technician = await this.getTechnicianById(technicianId, companyId);
      
      if (!technician.isActive || technician.status === 'offline') {
        return { available: false, reason: 'Technician not available' };
      }

      // Check working hours
      const dayOfWeek = date.toLocaleDateString('en-US', { weekday: 'lowercase' });
      const workingHours = JSON.parse(technician.workingHours || '{}');
      const daySchedule = workingHours[dayOfWeek];

      if (!daySchedule?.available) {
        return { available: false, reason: 'Not working on this day' };
      }

      // Check existing appointments
      const existingTickets = await this.getTechnicianSchedule(
        technicianId, 
        companyId, 
        date, 
        date
      );

      // Simple availability check (would need more sophisticated logic in production)
      const hasConflict = existingTickets.some(ticket => {
        if (!ticket.scheduledDate) return false;
        
        const ticketStart = new Date(ticket.scheduledDate);
        const ticketEnd = new Date(ticketStart.getTime() + (ticket.estimatedDuration || 60) * 60000);
        const requestEnd = new Date(date.getTime() + duration * 60000);
        
        return (date < ticketEnd && requestEnd > ticketStart);
      });

      return {
        available: !hasConflict,
        reason: hasConflict ? 'Time slot already booked' : undefined,
        suggestedTimes: hasConflict ? this.suggestAlternativeTimes(date, existingTickets) : undefined,
      };
    } catch (error) {
      console.error('Error checking technician availability:', error);
      throw new Error('Failed to check technician availability');
    }
  }

  // Suggest alternative times
  private suggestAlternativeTimes(requestedDate: Date, existingTickets: any[]): Date[] {
    const suggestions = [];
    const baseDate = new Date(requestedDate);
    baseDate.setHours(8, 0, 0, 0); // Start at 8 AM

    for (let hour = 8; hour < 18; hour++) {
      const suggestionTime = new Date(baseDate);
      suggestionTime.setHours(hour);

      const hasConflict = existingTickets.some(ticket => {
        if (!ticket.scheduledDate) return false;
        const ticketStart = new Date(ticket.scheduledDate);
        const timeDiff = Math.abs(suggestionTime.getTime() - ticketStart.getTime());
        return timeDiff < 2 * 60 * 60 * 1000; // 2 hour buffer
      });

      if (!hasConflict) {
        suggestions.push(new Date(suggestionTime));
      }

      if (suggestions.length >= 3) break;
    }

    return suggestions;
  }
}

export const technicianService = new TechnicianService();