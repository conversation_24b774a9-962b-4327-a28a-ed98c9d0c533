import OpenAI from 'openai';
import type { CalendarEventData } from './microsoft-graph';
import type { EventTypeType } from '../models/calendar';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export interface CalendarEventAnalysis {
  eventType: EventTypeType;
  priority: number; // 1-5 scale
  confidenceScore: number; // 0.0-1.0
  sentimentScore: number; // -1.0 to 1.0
  extractedCustomerInfo: {
    customerName?: string;
    companyName?: string;
    emailAddresses: string[];
    phoneNumbers: string[];
    location?: string;
    projectType?: string;
  };
  actionItems: string[];
  summary: string;
  topics: string[];
  urgency: 'low' | 'medium' | 'high';
  businessValue: 'low' | 'medium' | 'high';
}

export class CalendarAIAnalysisService {
  /**
   * Analyze a calendar event using AI to extract business insights
   */
  async analyzeCalendarEvent(event: CalendarEventData): Promise<CalendarEventAnalysis> {
    try {
      const eventText = this.extractEventText(event);
      const attendeeInfo = this.extractAttendeeInfo(event);
      
      const prompt = this.buildAnalysisPrompt(eventText, attendeeInfo, event);
      
      const completion = await openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: `You are an AI assistant specialized in analyzing business calendar events for an HVAC company called Fulmark. 
            Your job is to extract meaningful business insights, identify customer information, and categorize events.
            Always respond with valid JSON matching the specified schema.`,
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
        temperature: 0.3,
        max_tokens: 1500,
      });

      const response = completion.choices[0]?.message?.content;
      if (!response) {
        throw new Error('No response from OpenAI');
      }

      const analysis = JSON.parse(response) as CalendarEventAnalysis;
      
      // Validate and sanitize the response
      return this.validateAndSanitizeAnalysis(analysis, event);
    } catch (error) {
      console.error('Error analyzing calendar event:', error);
      
      // Return fallback analysis
      return this.createFallbackAnalysis(event);
    }
  }

  /**
   * Extract text content from calendar event
   */
  private extractEventText(event: CalendarEventData): string {
    const parts: string[] = [];
    
    if (event.subject) {
      parts.push(`Subject: ${event.subject}`);
    }
    
    if (event.body?.content) {
      // Strip HTML if content type is HTML
      const bodyContent = event.body.contentType === 'html' 
        ? this.stripHtml(event.body.content)
        : event.body.content;
      parts.push(`Body: ${bodyContent}`);
    }
    
    if (event.location?.displayName) {
      parts.push(`Location: ${event.location.displayName}`);
    }
    
    return parts.join('\n');
  }

  /**
   * Extract attendee information
   */
  private extractAttendeeInfo(event: CalendarEventData): string {
    const attendees: string[] = [];
    
    if (event.organizer?.emailAddress) {
      attendees.push(`Organizer: ${event.organizer.emailAddress.name || ''} <${event.organizer.emailAddress.address || ''}>`);
    }
    
    if (event.attendees) {
      event.attendees.forEach((attendee, index) => {
        if (attendee.emailAddress) {
          attendees.push(`Attendee ${index + 1}: ${attendee.emailAddress.name || ''} <${attendee.emailAddress.address || ''}>`);
        }
      });
    }
    
    return attendees.join('\n');
  }

  /**
   * Build the analysis prompt for OpenAI
   */
  private buildAnalysisPrompt(eventText: string, attendeeInfo: string, event: CalendarEventData): string {
    const startTime = event.start?.dateTime ? new Date(event.start.dateTime).toLocaleString() : 'Unknown';
    const endTime = event.end?.dateTime ? new Date(event.end.dateTime).toLocaleString() : 'Unknown';
    
    return `
Analyze this HVAC business calendar event and extract insights. Return a JSON object with the following structure:

{
  "eventType": "sales_meeting" | "support_call" | "installation" | "consultation" | "follow_up" | "maintenance" | "training" | "other",
  "priority": 1-5 (1=lowest, 5=highest),
  "confidenceScore": 0.0-1.0 (confidence in the analysis),
  "sentimentScore": -1.0 to 1.0 (-1=negative, 0=neutral, 1=positive),
  "extractedCustomerInfo": {
    "customerName": "extracted customer name if found",
    "companyName": "extracted company name if found",
    "emailAddresses": ["list", "of", "email", "addresses"],
    "phoneNumbers": ["list", "of", "phone", "numbers"],
    "location": "extracted location if relevant",
    "projectType": "type of HVAC project if mentioned"
  },
  "actionItems": ["list", "of", "action", "items", "extracted"],
  "summary": "brief summary of the event purpose",
  "topics": ["list", "of", "main", "topics", "discussed"],
  "urgency": "low" | "medium" | "high",
  "businessValue": "low" | "medium" | "high"
}

Event Details:
Time: ${startTime} - ${endTime}
Duration: ${event.isAllDay ? 'All Day' : 'Scheduled'}
Importance: ${event.importance || 'normal'}

Event Content:
${eventText}

Attendees:
${attendeeInfo}

Focus on:
1. Identifying if this is related to HVAC business (heating, ventilation, air conditioning, cooling, installation, maintenance, repair)
2. Extracting customer/prospect information
3. Determining the business purpose and value
4. Identifying any action items or follow-ups needed
5. Assessing urgency and priority for the business

Return only valid JSON, no additional text.`;
  }

  /**
   * Validate and sanitize the AI analysis response
   */
  private validateAndSanitizeAnalysis(analysis: CalendarEventAnalysis, event: CalendarEventData): CalendarEventAnalysis {
    // Ensure required fields have valid values
    const validEventTypes: EventTypeType[] = ['sales_meeting', 'support_call', 'installation', 'consultation', 'follow_up', 'maintenance', 'training', 'other'];
    
    return {
      eventType: validEventTypes.includes(analysis.eventType) ? analysis.eventType : 'other',
      priority: Math.max(1, Math.min(5, Math.round(analysis.priority || 3))),
      confidenceScore: Math.max(0, Math.min(1, analysis.confidenceScore || 0.5)),
      sentimentScore: Math.max(-1, Math.min(1, analysis.sentimentScore || 0)),
      extractedCustomerInfo: {
        customerName: analysis.extractedCustomerInfo?.customerName || undefined,
        companyName: analysis.extractedCustomerInfo?.companyName || undefined,
        emailAddresses: Array.isArray(analysis.extractedCustomerInfo?.emailAddresses) 
          ? analysis.extractedCustomerInfo.emailAddresses.filter(email => this.isValidEmail(email))
          : [],
        phoneNumbers: Array.isArray(analysis.extractedCustomerInfo?.phoneNumbers)
          ? analysis.extractedCustomerInfo.phoneNumbers.filter(phone => this.isValidPhone(phone))
          : [],
        location: analysis.extractedCustomerInfo?.location || undefined,
        projectType: analysis.extractedCustomerInfo?.projectType || undefined,
      },
      actionItems: Array.isArray(analysis.actionItems) ? analysis.actionItems.slice(0, 10) : [],
      summary: analysis.summary || 'Calendar event',
      topics: Array.isArray(analysis.topics) ? analysis.topics.slice(0, 10) : [],
      urgency: ['low', 'medium', 'high'].includes(analysis.urgency) ? analysis.urgency : 'medium',
      businessValue: ['low', 'medium', 'high'].includes(analysis.businessValue) ? analysis.businessValue : 'medium',
    };
  }

  /**
   * Create fallback analysis when AI analysis fails
   */
  private createFallbackAnalysis(event: CalendarEventData): CalendarEventAnalysis {
    const emailAddresses: string[] = [];
    
    // Extract emails from attendees
    if (event.attendees) {
      event.attendees.forEach(attendee => {
        if (attendee.emailAddress?.address) {
          emailAddresses.push(attendee.emailAddress.address);
        }
      });
    }
    
    if (event.organizer?.emailAddress?.address) {
      emailAddresses.push(event.organizer.emailAddress.address);
    }

    return {
      eventType: 'other',
      priority: 3,
      confidenceScore: 0.1,
      sentimentScore: 0,
      extractedCustomerInfo: {
        emailAddresses: [...new Set(emailAddresses)], // Remove duplicates
        phoneNumbers: [],
      },
      actionItems: [],
      summary: event.subject || 'Calendar event',
      topics: [],
      urgency: 'medium',
      businessValue: 'medium',
    };
  }

  /**
   * Strip HTML tags from content
   */
  private stripHtml(html: string): string {
    return html.replace(/<[^>]*>/g, '').replace(/&nbsp;/g, ' ').trim();
  }

  /**
   * Validate email address format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate phone number format (basic validation)
   */
  private isValidPhone(phone: string): boolean {
    const phoneRegex = /^[\+]?[\d\s\-\(\)]{7,}$/;
    return phoneRegex.test(phone.trim());
  }

  /**
   * Batch analyze multiple calendar events
   */
  async analyzeMultipleEvents(events: CalendarEventData[]): Promise<CalendarEventAnalysis[]> {
    const analyses: CalendarEventAnalysis[] = [];
    
    // Process in batches to avoid rate limits
    const batchSize = 5;
    for (let i = 0; i < events.length; i += batchSize) {
      const batch = events.slice(i, i + batchSize);
      const batchPromises = batch.map(event => this.analyzeCalendarEvent(event));
      
      try {
        const batchResults = await Promise.all(batchPromises);
        analyses.push(...batchResults);
      } catch (error) {
        console.error(`Error processing batch ${i / batchSize + 1}:`, error);
        // Add fallback analyses for failed batch
        const fallbackResults = batch.map(event => this.createFallbackAnalysis(event));
        analyses.push(...fallbackResults);
      }
      
      // Add delay between batches to respect rate limits
      if (i + batchSize < events.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    return analyses;
  }
}

// Singleton instance
export const calendarAIAnalysisService = new CalendarAIAnalysisService();
