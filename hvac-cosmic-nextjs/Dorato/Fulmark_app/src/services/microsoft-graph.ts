import { Client } from '@microsoft/microsoft-graph-client';
import { ConfidentialClientApplication } from '@azure/msal-node';
import type { Event, User } from '@microsoft/microsoft-graph-types';
import 'isomorphic-fetch';

// Microsoft Graph configuration
const msalConfig = {
  auth: {
    clientId: process.env.MICROSOFT_CLIENT_ID!,
    clientSecret: process.env.MICROSOFT_CLIENT_SECRET!,
    authority: `https://login.microsoftonline.com/${process.env.MICROSOFT_TENANT_ID || 'common'}`,
  },
};

// Required scopes for calendar access
export const CALENDAR_SCOPES = [
  'https://graph.microsoft.com/Calendars.Read',
  'https://graph.microsoft.com/Calendars.ReadWrite',
  'https://graph.microsoft.com/User.Read',
  'offline_access', // For refresh tokens
];

export class MicrosoftGraphService {
  private msalInstance: ConfidentialClientApplication;

  constructor() {
    this.msalInstance = new ConfidentialClientApplication(msalConfig);
  }

  /**
   * Get authorization URL for OAuth flow
   */
  async getAuthUrl(state?: string): Promise<string> {
    const authCodeUrlParameters = {
      scopes: CALENDAR_SCOPES,
      redirectUri: process.env.MICROSOFT_REDIRECT_URI!,
      state: state || '',
    };

    return await this.msalInstance.getAuthCodeUrl(authCodeUrlParameters);
  }

  /**
   * Exchange authorization code for tokens
   */
  async getTokenFromCode(code: string, state?: string) {
    const tokenRequest = {
      code,
      scopes: CALENDAR_SCOPES,
      redirectUri: process.env.MICROSOFT_REDIRECT_URI!,
    };

    try {
      const response = await this.msalInstance.acquireTokenByCode(tokenRequest);
      return {
        accessToken: response.accessToken,
        refreshToken: response.refreshToken,
        expiresOn: response.expiresOn,
        account: response.account,
        scopes: response.scopes,
      };
    } catch (error) {
      console.error('Error acquiring token:', error);
      throw new Error('Failed to acquire access token');
    }
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshToken(refreshToken: string) {
    const refreshTokenRequest = {
      refreshToken,
      scopes: CALENDAR_SCOPES,
    };

    try {
      const response = await this.msalInstance.acquireTokenByRefreshToken(refreshTokenRequest);
      return {
        accessToken: response.accessToken,
        refreshToken: response.refreshToken,
        expiresOn: response.expiresOn,
        account: response.account,
        scopes: response.scopes,
      };
    } catch (error) {
      console.error('Error refreshing token:', error);
      throw new Error('Failed to refresh access token');
    }
  }

  /**
   * Create authenticated Graph client
   */
  createGraphClient(accessToken: string): Client {
    return Client.init({
      authProvider: (done) => {
        done(null, accessToken);
      },
    });
  }

  /**
   * Get user profile information
   */
  async getUserProfile(accessToken: string): Promise<User> {
    const client = this.createGraphClient(accessToken);
    return await client.api('/me').get();
  }

  /**
   * Get calendar events for a date range
   */
  async getCalendarEvents(
    accessToken: string,
    startTime?: Date,
    endTime?: Date,
    top: number = 50
  ): Promise<Event[]> {
    const client = this.createGraphClient(accessToken);
    
    let query = client.api('/me/events').top(top).orderby('start/dateTime');
    
    // Add date filter if provided
    if (startTime && endTime) {
      const startISO = startTime.toISOString();
      const endISO = endTime.toISOString();
      query = query.filter(`start/dateTime ge '${startISO}' and end/dateTime le '${endISO}'`);
    }

    // Select specific fields to optimize performance
    query = query.select([
      'id',
      'subject',
      'body',
      'start',
      'end',
      'location',
      'attendees',
      'organizer',
      'isAllDay',
      'isCancelled',
      'importance',
      'sensitivity',
      'showAs',
      'lastModifiedDateTime',
      'changeKey',
      'webLink',
    ].join(','));

    const response = await query.get();
    return response.value || [];
  }

  /**
   * Get a specific calendar event by ID
   */
  async getCalendarEvent(accessToken: string, eventId: string): Promise<Event> {
    const client = this.createGraphClient(accessToken);
    return await client.api(`/me/events/${eventId}`).get();
  }

  /**
   * Get calendar events with delta query for incremental sync
   */
  async getCalendarEventsDelta(
    accessToken: string,
    deltaLink?: string
  ): Promise<{ events: Event[]; deltaLink: string }> {
    const client = this.createGraphClient(accessToken);
    
    let query;
    if (deltaLink) {
      // Use existing delta link for incremental sync
      query = client.api(deltaLink);
    } else {
      // Initial delta query
      query = client.api('/me/events/delta').top(50);
    }

    const response = await query.get();
    
    return {
      events: response.value || [],
      deltaLink: response['@odata.deltaLink'] || response['@odata.nextLink'] || '',
    };
  }

  /**
   * Subscribe to calendar change notifications (webhooks)
   */
  async createCalendarSubscription(
    accessToken: string,
    notificationUrl: string,
    expirationDateTime: Date
  ) {
    const client = this.createGraphClient(accessToken);
    
    const subscription = {
      changeType: 'created,updated,deleted',
      notificationUrl,
      resource: '/me/events',
      expirationDateTime: expirationDateTime.toISOString(),
      clientState: process.env.CALENDAR_WEBHOOK_SECRET || 'fulmark-calendar-webhook',
    };

    return await client.api('/subscriptions').post(subscription);
  }

  /**
   * Renew calendar subscription
   */
  async renewCalendarSubscription(
    accessToken: string,
    subscriptionId: string,
    expirationDateTime: Date
  ) {
    const client = this.createGraphClient(accessToken);
    
    const update = {
      expirationDateTime: expirationDateTime.toISOString(),
    };

    return await client.api(`/subscriptions/${subscriptionId}`).patch(update);
  }

  /**
   * Delete calendar subscription
   */
  async deleteCalendarSubscription(accessToken: string, subscriptionId: string) {
    const client = this.createGraphClient(accessToken);
    return await client.api(`/subscriptions/${subscriptionId}`).delete();
  }

  /**
   * Validate webhook notification
   */
  validateWebhookNotification(
    validationToken?: string,
    clientState?: string
  ): boolean {
    if (validationToken) {
      // This is a validation request from Microsoft Graph
      return true;
    }

    if (clientState !== (process.env.CALENDAR_WEBHOOK_SECRET || 'fulmark-calendar-webhook')) {
      return false;
    }

    return true;
  }
}

// Singleton instance
export const microsoftGraphService = new MicrosoftGraphService();

// Helper types for better TypeScript support
export interface CalendarEventData {
  id: string;
  subject?: string;
  body?: {
    content?: string;
    contentType?: string;
  };
  start?: {
    dateTime: string;
    timeZone: string;
  };
  end?: {
    dateTime: string;
    timeZone: string;
  };
  location?: {
    displayName?: string;
  };
  attendees?: Array<{
    emailAddress?: {
      name?: string;
      address?: string;
    };
    status?: {
      response?: string;
      time?: string;
    };
  }>;
  organizer?: {
    emailAddress?: {
      name?: string;
      address?: string;
    };
  };
  isAllDay?: boolean;
  isCancelled?: boolean;
  importance?: 'low' | 'normal' | 'high';
  sensitivity?: 'normal' | 'personal' | 'private' | 'confidential';
  showAs?: 'free' | 'tentative' | 'busy' | 'oof' | 'workingElsewhere' | 'unknown';
  lastModifiedDateTime?: string;
  changeKey?: string;
}

export interface WebhookNotification {
  subscriptionId: string;
  subscriptionExpirationDateTime: string;
  changeType: 'created' | 'updated' | 'deleted';
  resource: string;
  resourceData?: {
    id: string;
    '@odata.type': string;
    '@odata.id': string;
  };
  clientState?: string;
}
