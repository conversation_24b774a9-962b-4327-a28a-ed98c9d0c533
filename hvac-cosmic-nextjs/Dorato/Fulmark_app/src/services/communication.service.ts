// 📞 Communication Service - PEŁNA MOC WIATRU! ⚡
// Centralny hub dla wszystkich komunikacji z AI-powered insights

import { eq, and, desc, asc, like, sql, inArray } from 'drizzle-orm';
import { db } from '@/libs/DB';
import { 
  communications, 
  customers,
  serviceTickets,
  transcriptions,
  type communicationTypeEnum,
  type communicationDirectionEnum,
  type serviceTicketPriorityEnum 
} from '@/models/Schema';

export interface CreateCommunicationData {
  companyId: number;
  customerId?: number;
  serviceTicketId?: number;
  type: typeof communicationTypeEnum.enumValues[number];
  direction: typeof communicationDirectionEnum.enumValues[number];
  subject?: string;
  content: string;
  fromEmail?: string;
  toEmail?: string;
  fromPhone?: string;
  toPhone?: string;
  attachments?: string[];
  threadId?: string;
  parentId?: number;
}

export interface UpdateCommunicationData extends Partial<CreateCommunicationData> {
  sentimentScore?: number;
  category?: string;
  priority?: typeof serviceTicketPriorityEnum.enumValues[number];
  intent?: string;
  autoResponseSent?: boolean;
  requiresHumanAttention?: boolean;
  isProcessed?: boolean;
  transcriptionId?: number;
}

export interface CommunicationFilters {
  companyId: number;
  customerId?: number;
  serviceTicketId?: number;
  type?: typeof communicationTypeEnum.enumValues[number][];
  direction?: typeof communicationDirectionEnum.enumValues[number];
  category?: string;
  priority?: typeof serviceTicketPriorityEnum.enumValues[number][];
  isProcessed?: boolean;
  requiresHumanAttention?: boolean;
  dateFrom?: Date;
  dateTo?: Date;
  search?: string;
}

export interface CommunicationAnalytics {
  totalCommunications: number;
  unprocessedCount: number;
  requiresAttentionCount: number;
  averageSentiment: number;
  typeDistribution: Record<string, number>;
  categoryDistribution: Record<string, number>;
  priorityDistribution: Record<string, number>;
  responseTimeStats: {
    average: number;
    median: number;
    fastest: number;
    slowest: number;
  };
}

export class CommunicationService {
  
  // Create new communication
  async createCommunication(data: CreateCommunicationData) {
    try {
      const [communication] = await db.insert(communications).values({
        ...data,
        attachments: data.attachments ? JSON.stringify(data.attachments) : null,
      }).returning();

      // Auto-process if it's an inbound communication
      if (data.direction === 'inbound') {
        await this.processInboundCommunication(communication.id);
      }

      return communication;
    } catch (error) {
      console.error('Error creating communication:', error);
      throw new Error('Failed to create communication');
    }
  }

  // Get communication by ID
  async getCommunicationById(id: number, companyId: number) {
    try {
      const communication = await db.query.communications.findFirst({
        where: and(eq(communications.id, id), eq(communications.companyId, companyId)),
        with: {
          customer: true,
          serviceTicket: true,
          parent: true,
          replies: {
            orderBy: asc(communications.createdAt),
          }
        }
      });

      if (!communication) {
        throw new Error('Communication not found');
      }

      return communication;
    } catch (error) {
      console.error('Error fetching communication:', error);
      throw new Error('Failed to fetch communication');
    }
  }

  // Get communications with filters
  async getCommunications(filters: CommunicationFilters, page = 1, limit = 20) {
    try {
      const offset = (page - 1) * limit;
      const conditions = [eq(communications.companyId, filters.companyId)];

      // Apply filters
      if (filters.customerId) {
        conditions.push(eq(communications.customerId, filters.customerId));
      }

      if (filters.serviceTicketId) {
        conditions.push(eq(communications.serviceTicketId, filters.serviceTicketId));
      }

      if (filters.type && filters.type.length > 0) {
        conditions.push(inArray(communications.type, filters.type));
      }

      if (filters.direction) {
        conditions.push(eq(communications.direction, filters.direction));
      }

      if (filters.category) {
        conditions.push(like(communications.category, `%${filters.category}%`));
      }

      if (filters.priority && filters.priority.length > 0) {
        conditions.push(inArray(communications.priority, filters.priority));
      }

      if (filters.isProcessed !== undefined) {
        conditions.push(eq(communications.isProcessed, filters.isProcessed));
      }

      if (filters.requiresHumanAttention !== undefined) {
        conditions.push(eq(communications.requiresHumanAttention, filters.requiresHumanAttention));
      }

      if (filters.dateFrom) {
        conditions.push(sql`${communications.createdAt} >= ${filters.dateFrom}`);
      }

      if (filters.dateTo) {
        conditions.push(sql`${communications.createdAt} <= ${filters.dateTo}`);
      }

      if (filters.search) {
        conditions.push(
          sql`(${communications.subject} ILIKE ${`%${filters.search}%`} OR 
               ${communications.content} ILIKE ${`%${filters.search}%`} OR 
               ${communications.fromEmail} ILIKE ${`%${filters.search}%`} OR 
               ${communications.toEmail} ILIKE ${`%${filters.search}%`})`
        );
      }

      const whereClause = conditions.length > 1 ? and(...conditions) : conditions[0];

      // Get communications with pagination
      const communicationsList = await db.query.communications.findMany({
        where: whereClause,
        orderBy: desc(communications.createdAt),
        limit,
        offset,
        with: {
          customer: true,
          serviceTicket: true,
        }
      });

      // Get total count
      const [{ count }] = await db
        .select({ count: sql<number>`count(*)` })
        .from(communications)
        .where(whereClause);

      return {
        communications: communicationsList,
        total: count,
        page,
        limit,
        totalPages: Math.ceil(count / limit),
      };
    } catch (error) {
      console.error('Error fetching communications:', error);
      throw new Error('Failed to fetch communications');
    }
  }

  // Update communication
  async updateCommunication(id: number, companyId: number, data: UpdateCommunicationData) {
    try {
      const [communication] = await db
        .update(communications)
        .set({
          ...data,
          attachments: data.attachments ? JSON.stringify(data.attachments) : undefined,
          updatedAt: new Date(),
        })
        .where(and(eq(communications.id, id), eq(communications.companyId, companyId)))
        .returning();

      if (!communication) {
        throw new Error('Communication not found');
      }

      return communication;
    } catch (error) {
      console.error('Error updating communication:', error);
      throw new Error('Failed to update communication');
    }
  }

  // Process inbound communication with AI
  async processInboundCommunication(communicationId: number) {
    try {
      const communication = await db.query.communications.findFirst({
        where: eq(communications.id, communicationId),
        with: {
          customer: true,
          serviceTicket: true,
        }
      });

      if (!communication) {
        throw new Error('Communication not found');
      }

      // AI Analysis would go here (OpenAI integration)
      // For now, we'll use basic keyword analysis
      const analysis = await this.analyzeContent(communication.content);

      // Update communication with AI insights
      await this.updateCommunication(communicationId, communication.companyId, {
        sentimentScore: analysis.sentiment,
        category: analysis.category,
        priority: analysis.priority,
        intent: analysis.intent,
        requiresHumanAttention: analysis.requiresAttention,
        isProcessed: true,
      });

      // Auto-create service ticket if needed
      if (analysis.shouldCreateTicket && communication.customerId) {
        await this.createServiceTicketFromCommunication(communication);
      }

      return analysis;
    } catch (error) {
      console.error('Error processing inbound communication:', error);
      throw new Error('Failed to process communication');
    }
  }

  // Basic content analysis (will be replaced with OpenAI)
  private async analyzeContent(content: string) {
    const lowerContent = content.toLowerCase();
    
    // Sentiment analysis (basic)
    let sentiment = 0;
    const positiveWords = ['dziękuję', 'świetnie', 'doskonale', 'zadowolony', 'polecam'];
    const negativeWords = ['problem', 'awaria', 'nie działa', 'zepsuty', 'pilne', 'natychmiast'];
    
    positiveWords.forEach(word => {
      if (lowerContent.includes(word)) sentiment += 0.2;
    });
    
    negativeWords.forEach(word => {
      if (lowerContent.includes(word)) sentiment -= 0.3;
    });
    
    sentiment = Math.max(-1, Math.min(1, sentiment));

    // Category detection
    let category = 'inquiry';
    if (lowerContent.includes('awaria') || lowerContent.includes('nie działa')) {
      category = 'emergency';
    } else if (lowerContent.includes('serwis') || lowerContent.includes('przegląd')) {
      category = 'maintenance';
    } else if (lowerContent.includes('wycena') || lowerContent.includes('oferta')) {
      category = 'quote_request';
    } else if (lowerContent.includes('reklamacja') || lowerContent.includes('problem')) {
      category = 'complaint';
    }

    // Priority detection
    let priority: typeof serviceTicketPriorityEnum.enumValues[number] = 'normal';
    if (lowerContent.includes('pilne') || lowerContent.includes('natychmiast') || category === 'emergency') {
      priority = 'urgent';
    } else if (lowerContent.includes('awaria')) {
      priority = 'high';
    }

    // Intent detection
    let intent = 'general_inquiry';
    if (category === 'quote_request') intent = 'request_quote';
    else if (category === 'emergency') intent = 'report_emergency';
    else if (category === 'maintenance') intent = 'schedule_maintenance';
    else if (category === 'complaint') intent = 'file_complaint';

    return {
      sentiment,
      category,
      priority,
      intent,
      requiresAttention: sentiment < -0.3 || priority === 'urgent' || category === 'emergency',
      shouldCreateTicket: category === 'emergency' || category === 'maintenance' || lowerContent.includes('serwis'),
    };
  }

  // Create service ticket from communication
  private async createServiceTicketFromCommunication(communication: any) {
    try {
      // This would integrate with ServiceTicketService
      // For now, we'll just log the intent
      console.log(`Should create service ticket for communication ${communication.id}`);
      
      // TODO: Integrate with ServiceTicketService.createServiceTicket()
      
    } catch (error) {
      console.error('Error creating service ticket from communication:', error);
    }
  }

  // Get communication thread
  async getCommunicationThread(threadId: string, companyId: number) {
    try {
      const thread = await db.query.communications.findMany({
        where: and(
          eq(communications.threadId, threadId),
          eq(communications.companyId, companyId)
        ),
        orderBy: asc(communications.createdAt),
        with: {
          customer: true,
          serviceTicket: true,
        }
      });

      return thread;
    } catch (error) {
      console.error('Error fetching communication thread:', error);
      throw new Error('Failed to fetch communication thread');
    }
  }

  // Get communication analytics
  async getCommunicationAnalytics(companyId: number): Promise<CommunicationAnalytics> {
    try {
      // Total communications
      const [{ totalCommunications }] = await db
        .select({ totalCommunications: sql<number>`count(*)` })
        .from(communications)
        .where(eq(communications.companyId, companyId));

      // Unprocessed count
      const [{ unprocessedCount }] = await db
        .select({ unprocessedCount: sql<number>`count(*)` })
        .from(communications)
        .where(and(
          eq(communications.companyId, companyId),
          eq(communications.isProcessed, false)
        ));

      // Requires attention count
      const [{ requiresAttentionCount }] = await db
        .select({ requiresAttentionCount: sql<number>`count(*)` })
        .from(communications)
        .where(and(
          eq(communications.companyId, companyId),
          eq(communications.requiresHumanAttention, true)
        ));

      // Average sentiment
      const [{ averageSentiment }] = await db
        .select({ averageSentiment: sql<number>`avg(${communications.sentimentScore})` })
        .from(communications)
        .where(and(
          eq(communications.companyId, companyId),
          sql`${communications.sentimentScore} IS NOT NULL`
        ));

      // Type distribution
      const typeData = await db
        .select({
          type: communications.type,
          count: sql<number>`count(*)`
        })
        .from(communications)
        .where(eq(communications.companyId, companyId))
        .groupBy(communications.type);

      const typeDistribution: Record<string, number> = {};
      typeData.forEach(item => {
        typeDistribution[item.type] = item.count;
      });

      // Category distribution
      const categoryData = await db
        .select({
          category: communications.category,
          count: sql<number>`count(*)`
        })
        .from(communications)
        .where(and(
          eq(communications.companyId, companyId),
          sql`${communications.category} IS NOT NULL`
        ))
        .groupBy(communications.category);

      const categoryDistribution: Record<string, number> = {};
      categoryData.forEach(item => {
        if (item.category) {
          categoryDistribution[item.category] = item.count;
        }
      });

      // Priority distribution
      const priorityData = await db
        .select({
          priority: communications.priority,
          count: sql<number>`count(*)`
        })
        .from(communications)
        .where(and(
          eq(communications.companyId, companyId),
          sql`${communications.priority} IS NOT NULL`
        ))
        .groupBy(communications.priority);

      const priorityDistribution: Record<string, number> = {};
      priorityData.forEach(item => {
        if (item.priority) {
          priorityDistribution[item.priority] = item.count;
        }
      });

      // Response time stats (mock data for now)
      const responseTimeStats = {
        average: 2.5, // hours
        median: 1.8,
        fastest: 0.2,
        slowest: 24.0,
      };

      return {
        totalCommunications,
        unprocessedCount,
        requiresAttentionCount,
        averageSentiment: Number(averageSentiment || 0),
        typeDistribution,
        categoryDistribution,
        priorityDistribution,
        responseTimeStats,
      };
    } catch (error) {
      console.error('Error fetching communication analytics:', error);
      throw new Error('Failed to fetch communication analytics');
    }
  }

  // Mark communication as processed
  async markAsProcessed(id: number, companyId: number) {
    try {
      return await this.updateCommunication(id, companyId, {
        isProcessed: true,
      });
    } catch (error) {
      console.error('Error marking communication as processed:', error);
      throw new Error('Failed to mark communication as processed');
    }
  }

  // Mark communication as requiring attention
  async markAsRequiresAttention(id: number, companyId: number, requires = true) {
    try {
      return await this.updateCommunication(id, companyId, {
        requiresHumanAttention: requires,
      });
    } catch (error) {
      console.error('Error updating attention flag:', error);
      throw new Error('Failed to update attention flag');
    }
  }
}

export const communicationService = new CommunicationService();