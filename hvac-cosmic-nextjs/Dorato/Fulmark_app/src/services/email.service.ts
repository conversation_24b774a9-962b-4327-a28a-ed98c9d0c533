// 📧 Email Service - PEŁNA MOC WIATRU! ⚡
// AI-powered email processing with OpenAI GPT-4 integration

import OpenAI from 'openai';
import { Resend } from 'resend';
import { communicationService } from './communication.service';
import { customerService } from './customer.service';

// Initialize OpenAI (will be conditional based on env var)
const openai = process.env.OPENAI_API_KEY ? new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
}) : null;

// Initialize Resend
const resend = process.env.RESEND_API_KEY ? new Resend(process.env.RESEND_API_KEY) : null;

export interface EmailData {
  from: string;
  to: string;
  subject: string;
  content: string;
  attachments?: Array<{
    filename: string;
    content: string;
    contentType: string;
  }>;
  messageId?: string;
  threadId?: string;
}

export interface EmailAnalysis {
  sentiment: number; // -1 to 1
  category: string;
  priority: 'low' | 'normal' | 'high' | 'urgent' | 'emergency';
  intent: string;
  urgencyLevel: number; // 0-100
  requiresHumanAttention: boolean;
  suggestedResponse?: string;
  extractedInfo: {
    customerName?: string;
    phoneNumber?: string;
    address?: string;
    equipmentType?: string;
    problemDescription?: string;
    preferredDate?: string;
  };
}

export interface SendEmailData {
  to: string;
  subject: string;
  content: string;
  companyId: number;
  customerId?: number;
  serviceTicketId?: number;
  templateType?: 'quote' | 'confirmation' | 'reminder' | 'follow_up' | 'emergency_response';
  templateData?: Record<string, any>;
}

export class EmailService {
  
  // Process inbound email with AI analysis
  async processInboundEmail(emailData: EmailData, companyId: number): Promise<EmailAnalysis> {
    try {
      console.log('Processing inbound email:', emailData.subject);
      
      // Find or create customer
      let customer = await customerService.findByEmail(emailData.from, companyId);
      
      if (!customer) {
        // Create new lead
        customer = await customerService.createLead({
          companyId,
          email: emailData.from,
          source: 'email',
          initialMessage: emailData.content,
          subject: emailData.subject,
        });
      }

      // Analyze email content with AI
      const analysis = await this.analyzeEmailContent(emailData);
      
      // Create communication record
      await communicationService.createCommunication({
        companyId,
        customerId: customer.id,
        type: 'email',
        direction: 'inbound',
        subject: emailData.subject,
        content: emailData.content,
        fromEmail: emailData.from,
        toEmail: emailData.to,
        threadId: emailData.threadId,
        attachments: emailData.attachments?.map(att => att.filename),
      });

      // Send auto-response if appropriate
      if (analysis.category === 'emergency' || analysis.priority === 'urgent') {
        await this.sendEmergencyAutoResponse(emailData.from, customer, companyId);
      } else if (analysis.category === 'quote_request') {
        await this.sendQuoteRequestAutoResponse(emailData.from, customer, companyId);
      } else {
        await this.sendGeneralAutoResponse(emailData.from, customer, companyId);
      }

      return analysis;
    } catch (error) {
      console.error('Error processing inbound email:', error);
      throw new Error('Failed to process inbound email');
    }
  }

  // Analyze email content with OpenAI GPT-4
  private async analyzeEmailContent(emailData: EmailData): Promise<EmailAnalysis> {
    try {
      if (!openai) {
        // Fallback to basic analysis if OpenAI is not configured
        return this.basicEmailAnalysis(emailData);
      }

      const prompt = `
Analyze this HVAC service email and provide a JSON response with the following structure:

{
  "sentiment": number (-1 to 1),
  "category": "emergency" | "quote_request" | "maintenance" | "complaint" | "inquiry" | "follow_up",
  "priority": "low" | "normal" | "high" | "urgent" | "emergency",
  "intent": string,
  "urgencyLevel": number (0-100),
  "requiresHumanAttention": boolean,
  "suggestedResponse": string,
  "extractedInfo": {
    "customerName": string | null,
    "phoneNumber": string | null,
    "address": string | null,
    "equipmentType": string | null,
    "problemDescription": string | null,
    "preferredDate": string | null
  }
}

Email Subject: ${emailData.subject}
Email Content: ${emailData.content}

Consider Polish language context and HVAC industry specifics. Emergency keywords include: "awaria", "nie działa", "pilne", "natychmiast", "zimno", "gorąco", "brak ogrzewania", "brak klimatyzacji".
`;

      const completion = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: "You are an AI assistant specialized in analyzing HVAC service emails. Respond only with valid JSON."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 1000,
      });

      const response = completion.choices[0]?.message?.content;
      if (!response) {
        throw new Error('No response from OpenAI');
      }

      try {
        return JSON.parse(response);
      } catch (parseError) {
        console.error('Error parsing OpenAI response:', parseError);
        return this.basicEmailAnalysis(emailData);
      }
    } catch (error) {
      console.error('Error with OpenAI analysis:', error);
      return this.basicEmailAnalysis(emailData);
    }
  }

  // Basic email analysis fallback
  private basicEmailAnalysis(emailData: EmailData): EmailAnalysis {
    const content = emailData.content.toLowerCase();
    const subject = emailData.subject.toLowerCase();
    const fullText = `${subject} ${content}`;

    // Sentiment analysis
    let sentiment = 0;
    const positiveWords = ['dziękuję', 'świetnie', 'doskonale', 'zadowolony', 'polecam', 'profesjonalnie'];
    const negativeWords = ['problem', 'awaria', 'nie działa', 'zepsuty', 'źle', 'niezadowolony', 'reklamacja'];
    
    positiveWords.forEach(word => {
      if (fullText.includes(word)) sentiment += 0.2;
    });
    
    negativeWords.forEach(word => {
      if (fullText.includes(word)) sentiment -= 0.3;
    });
    
    sentiment = Math.max(-1, Math.min(1, sentiment));

    // Category detection
    let category = 'inquiry';
    if (fullText.includes('awaria') || fullText.includes('nie działa') || fullText.includes('pilne')) {
      category = 'emergency';
    } else if (fullText.includes('wycena') || fullText.includes('oferta') || fullText.includes('cennik')) {
      category = 'quote_request';
    } else if (fullText.includes('serwis') || fullText.includes('przegląd') || fullText.includes('konserwacja')) {
      category = 'maintenance';
    } else if (fullText.includes('reklamacja') || fullText.includes('problem') || fullText.includes('skarga')) {
      category = 'complaint';
    }

    // Priority detection
    let priority: 'low' | 'normal' | 'high' | 'urgent' | 'emergency' = 'normal';
    let urgencyLevel = 50;
    
    if (fullText.includes('natychmiast') || fullText.includes('pilne') || category === 'emergency') {
      priority = 'urgent';
      urgencyLevel = 90;
    } else if (fullText.includes('awaria') || fullText.includes('nie działa')) {
      priority = 'high';
      urgencyLevel = 75;
    } else if (category === 'complaint') {
      priority = 'high';
      urgencyLevel = 70;
    }

    // Intent detection
    let intent = 'general_inquiry';
    if (category === 'quote_request') intent = 'request_quote';
    else if (category === 'emergency') intent = 'report_emergency';
    else if (category === 'maintenance') intent = 'schedule_maintenance';
    else if (category === 'complaint') intent = 'file_complaint';

    // Extract basic info
    const phoneRegex = /(\+?48\s?)?(\d{3}[\s-]?\d{3}[\s-]?\d{3})/g;
    const phoneMatch = fullText.match(phoneRegex);
    
    return {
      sentiment,
      category,
      priority,
      intent,
      urgencyLevel,
      requiresHumanAttention: sentiment < -0.3 || priority === 'urgent' || category === 'emergency',
      extractedInfo: {
        phoneNumber: phoneMatch ? phoneMatch[0] : undefined,
        problemDescription: category === 'emergency' ? content.substring(0, 200) : undefined,
      }
    };
  }

  // Send emergency auto-response
  private async sendEmergencyAutoResponse(to: string, customer: any, companyId: number) {
    const subject = `🚨 Potwierdzenie zgłoszenia awarii - ${process.env.COMPANY_NAME}`;
    const content = `
Szanowny/a ${customer.firstName} ${customer.lastName},

Otrzymaliśmy Państwa zgłoszenie awarii i traktujemy je priorytetowo.

🔧 **Status:** Zgłoszenie przyjęte
⏰ **Czas reakcji:** Do 2 godzin
📞 **Kontakt awaryjny:** ${process.env.EMERGENCY_PHONE}

Nasz technik skontaktuje się z Państwem w ciągu najbliższych 2 godzin w celu ustalenia szczegółów i terminu wizyty.

W przypadku pilnych pytań prosimy o kontakt pod numerem awaryjnym.

Z poważaniem,
Zespół ${process.env.COMPANY_NAME}
`;

    await this.sendEmail({
      to,
      subject,
      content,
      companyId,
      customerId: customer.id,
      templateType: 'emergency_response',
    });
  }

  // Send quote request auto-response
  private async sendQuoteRequestAutoResponse(to: string, customer: any, companyId: number) {
    const subject = `💰 Potwierdzenie prośby o wycenę - ${process.env.COMPANY_NAME}`;
    const content = `
Szanowny/a ${customer.firstName} ${customer.lastName},

Dziękujemy za zainteresowanie naszymi usługami!

📋 **Następne kroki:**
1. Nasz specjalista przeanalizuje Państwa zapytanie
2. Skontaktujemy się w ciągu 24 godzin
3. Umówimy wizytę w celu przygotowania szczegółowej wyceny

📞 **Kontakt:** ${process.env.COMPANY_PHONE}
📧 **Email:** ${process.env.COMPANY_EMAIL}

Jesteśmy do Państwa dyspozycji od poniedziałku do piątku w godzinach 8:00-18:00.

Z poważaniem,
Zespół ${process.env.COMPANY_NAME}
`;

    await this.sendEmail({
      to,
      subject,
      content,
      companyId,
      customerId: customer.id,
      templateType: 'quote',
    });
  }

  // Send general auto-response
  private async sendGeneralAutoResponse(to: string, customer: any, companyId: number) {
    const subject = `✅ Potwierdzenie otrzymania wiadomości - ${process.env.COMPANY_NAME}`;
    const content = `
Szanowny/a ${customer.firstName} ${customer.lastName},

Dziękujemy za kontakt z ${process.env.COMPANY_NAME}.

Otrzymaliśmy Państwa wiadomość i odpowiemy na nią w ciągu 24 godzin roboczych.

📞 **Kontakt:** ${process.env.COMPANY_PHONE}
📧 **Email:** ${process.env.COMPANY_EMAIL}

W przypadku pilnych spraw prosimy o kontakt telefoniczny.

Z poważaniem,
Zespół ${process.env.COMPANY_NAME}
`;

    await this.sendEmail({
      to,
      subject,
      content,
      companyId,
      customerId: customer.id,
      templateType: 'confirmation',
    });
  }

  // Send email using Resend
  async sendEmail(data: SendEmailData) {
    try {
      if (!resend) {
        console.log('Resend not configured, email would be sent:', data);
        return { success: true, messageId: 'mock-id' };
      }

      const result = await resend.emails.send({
        from: `${process.env.COMPANY_NAME} <noreply@${process.env.COMPANY_EMAIL?.split('@')[1] || 'example.com'}>`,
        to: data.to,
        subject: data.subject,
        html: this.formatEmailContent(data.content),
      });

      // Create outbound communication record
      await communicationService.createCommunication({
        companyId: data.companyId,
        customerId: data.customerId,
        serviceTicketId: data.serviceTicketId,
        type: 'email',
        direction: 'outbound',
        subject: data.subject,
        content: data.content,
        fromEmail: process.env.COMPANY_EMAIL || '<EMAIL>',
        toEmail: data.to,
      });

      return result;
    } catch (error) {
      console.error('Error sending email:', error);
      throw new Error('Failed to send email');
    }
  }

  // Format email content as HTML
  private formatEmailContent(content: string): string {
    return `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Email from ${process.env.COMPANY_NAME}</title>
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
    .header { background: #2563eb; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
    .content { background: #f9fafb; padding: 30px; border-radius: 0 0 8px 8px; }
    .footer { text-align: center; margin-top: 20px; font-size: 12px; color: #666; }
    .highlight { background: #dbeafe; padding: 15px; border-radius: 6px; margin: 15px 0; }
  </style>
</head>
<body>
  <div class="header">
    <h1>${process.env.COMPANY_NAME}</h1>
    <p>Profesjonalne usługi HVAC</p>
  </div>
  <div class="content">
    ${content.replace(/\n/g, '<br>')}
  </div>
  <div class="footer">
    <p>© 2024 ${process.env.COMPANY_NAME}. Wszystkie prawa zastrzeżone.</p>
    <p>📞 ${process.env.COMPANY_PHONE} | 📧 ${process.env.COMPANY_EMAIL}</p>
  </div>
</body>
</html>
`;
  }

  // Generate AI-powered response suggestion
  async generateResponseSuggestion(originalEmail: EmailData, context?: string): Promise<string> {
    try {
      if (!openai) {
        return this.generateBasicResponse(originalEmail);
      }

      const prompt = `
Generate a professional email response for an HVAC service company in Polish. 

Original email:
Subject: ${originalEmail.subject}
Content: ${originalEmail.content}

Context: ${context || 'Standard customer inquiry'}

Requirements:
- Professional and friendly tone
- Address customer concerns specifically
- Include next steps
- Add company contact information
- Keep it concise but informative
- Use Polish language
`;

      const completion = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: "You are a professional customer service representative for an HVAC company. Write helpful, clear responses in Polish."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 500,
      });

      return completion.choices[0]?.message?.content || this.generateBasicResponse(originalEmail);
    } catch (error) {
      console.error('Error generating AI response:', error);
      return this.generateBasicResponse(originalEmail);
    }
  }

  // Basic response generation fallback
  private generateBasicResponse(originalEmail: EmailData): string {
    return `
Szanowny Kliencie,

Dziękujemy za kontakt w sprawie: ${originalEmail.subject}

Otrzymaliśmy Państwa wiadomość i nasz zespół przeanalizuje ją w ciągu 24 godzin roboczych. Skontaktujemy się z Państwem z odpowiedzią lub w celu umówienia wizyty technicznej.

W przypadku pilnych spraw prosimy o kontakt telefoniczny: ${process.env.COMPANY_PHONE}

Z poważaniem,
Zespół ${process.env.COMPANY_NAME}
`;
  }
}

export const emailService = new EmailService();