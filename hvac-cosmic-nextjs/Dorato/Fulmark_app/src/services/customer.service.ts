// 🌟 HVAC Customer Service - PEŁNA MOC WIATRU! ⚡
// Complete customer management with AI-powered insights

import { eq, and, desc, asc, like, sql } from 'drizzle-orm';
import { db } from '@/libs/DB';
import { 
  customers, 
  communications, 
  serviceTickets, 
  invoices,
  transcriptions,
  type customerFlowStateEnum 
} from '@/models/Schema';

export interface CreateCustomerData {
  companyId: number;
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  alternatePhone?: string;
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;
  preferredContact?: 'email' | 'phone' | 'sms' | 'in_person' | 'chat';
  source?: string;
  notes?: string;
  tags?: string[];
}

export interface UpdateCustomerData extends Partial<CreateCustomerData> {
  flowState?: typeof customerFlowStateEnum.enumValues[number];
  customerValue?: number;
  lastServiceDate?: Date;
  nextMaintenanceDate?: Date;
  healthScore?: number;
  churnRisk?: number;
  sentimentScore?: number;
}

export interface CustomerFilters {
  companyId: number;
  search?: string;
  flowState?: typeof customerFlowStateEnum.enumValues[number];
  city?: string;
  isActive?: boolean;
  healthScoreMin?: number;
  healthScoreMax?: number;
  churnRiskMin?: number;
  churnRiskMax?: number;
}

export interface CustomerAnalytics {
  totalCustomers: number;
  newCustomersThisMonth: number;
  activeCustomers: number;
  averageHealthScore: number;
  averageCustomerValue: number;
  churnRiskDistribution: {
    low: number;
    medium: number;
    high: number;
  };
  flowStateDistribution: Record<string, number>;
  topCities: Array<{ city: string; count: number }>;
}

export class CustomerService {
  
  // Create new customer
  async createCustomer(data: CreateCustomerData) {
    try {
      const [customer] = await db.insert(customers).values({
        ...data,
        tags: data.tags ? JSON.stringify(data.tags) : null,
      }).returning();

      return customer;
    } catch (error) {
      console.error('Error creating customer:', error);
      throw new Error('Failed to create customer');
    }
  }

  // Get customer by ID with related data
  async getCustomerById(id: number, companyId: number) {
    try {
      const customer = await db.query.customers.findFirst({
        where: and(eq(customers.id, id), eq(customers.companyId, companyId)),
        with: {
          buildings: true,
          serviceTickets: {
            orderBy: desc(serviceTickets.createdAt),
            limit: 10,
            with: {
              assignedTechnician: true,
            }
          },
          communications: {
            orderBy: desc(communications.createdAt),
            limit: 20,
          },
          invoices: {
            orderBy: desc(invoices.createdAt),
            limit: 10,
          },
          transcriptions: {
            orderBy: desc(transcriptions.createdAt),
            limit: 5,
          }
        }
      });

      if (!customer) {
        throw new Error('Customer not found');
      }

      return customer;
    } catch (error) {
      console.error('Error fetching customer:', error);
      throw new Error('Failed to fetch customer');
    }
  }

  // Get customers with filters and pagination
  async getCustomers(filters: CustomerFilters, page = 1, limit = 20) {
    try {
      const offset = (page - 1) * limit;
      const conditions = [eq(customers.companyId, filters.companyId)];

      // Apply filters
      if (filters.search) {
        conditions.push(
          sql`(${customers.firstName} ILIKE ${`%${filters.search}%`} OR 
               ${customers.lastName} ILIKE ${`%${filters.search}%`} OR 
               ${customers.email} ILIKE ${`%${filters.search}%`} OR 
               ${customers.phone} ILIKE ${`%${filters.search}%`})`
        );
      }

      if (filters.flowState) {
        conditions.push(eq(customers.flowState, filters.flowState));
      }

      if (filters.city) {
        conditions.push(like(customers.city, `%${filters.city}%`));
      }

      if (filters.isActive !== undefined) {
        conditions.push(eq(customers.isActive, filters.isActive));
      }

      if (filters.healthScoreMin !== undefined) {
        conditions.push(sql`${customers.healthScore} >= ${filters.healthScoreMin}`);
      }

      if (filters.healthScoreMax !== undefined) {
        conditions.push(sql`${customers.healthScore} <= ${filters.healthScoreMax}`);
      }

      if (filters.churnRiskMin !== undefined) {
        conditions.push(sql`${customers.churnRisk} >= ${filters.churnRiskMin}`);
      }

      if (filters.churnRiskMax !== undefined) {
        conditions.push(sql`${customers.churnRisk} <= ${filters.churnRiskMax}`);
      }

      const whereClause = conditions.length > 1 ? and(...conditions) : conditions[0];

      // Get customers with pagination
      const customersList = await db.query.customers.findMany({
        where: whereClause,
        orderBy: desc(customers.createdAt),
        limit,
        offset,
        with: {
          serviceTickets: {
            limit: 1,
            orderBy: desc(serviceTickets.createdAt),
          }
        }
      });

      // Get total count
      const [{ count }] = await db
        .select({ count: sql<number>`count(*)` })
        .from(customers)
        .where(whereClause);

      return {
        customers: customersList,
        total: count,
        page,
        limit,
        totalPages: Math.ceil(count / limit),
      };
    } catch (error) {
      console.error('Error fetching customers:', error);
      throw new Error('Failed to fetch customers');
    }
  }

  // Update customer
  async updateCustomer(id: number, companyId: number, data: UpdateCustomerData) {
    try {
      const [customer] = await db
        .update(customers)
        .set({
          ...data,
          tags: data.tags ? JSON.stringify(data.tags) : undefined,
          updatedAt: new Date(),
        })
        .where(and(eq(customers.id, id), eq(customers.companyId, companyId)))
        .returning();

      if (!customer) {
        throw new Error('Customer not found');
      }

      return customer;
    } catch (error) {
      console.error('Error updating customer:', error);
      throw new Error('Failed to update customer');
    }
  }

  // Delete customer (soft delete)
  async deleteCustomer(id: number, companyId: number) {
    try {
      const [customer] = await db
        .update(customers)
        .set({ isActive: false, updatedAt: new Date() })
        .where(and(eq(customers.id, id), eq(customers.companyId, companyId)))
        .returning();

      if (!customer) {
        throw new Error('Customer not found');
      }

      return customer;
    } catch (error) {
      console.error('Error deleting customer:', error);
      throw new Error('Failed to delete customer');
    }
  }

  // Find customer by email
  async findByEmail(email: string, companyId: number) {
    try {
      return await db.query.customers.findFirst({
        where: and(
          eq(customers.email, email),
          eq(customers.companyId, companyId),
          eq(customers.isActive, true)
        ),
      });
    } catch (error) {
      console.error('Error finding customer by email:', error);
      return null;
    }
  }

  // Find customer by phone
  async findByPhone(phone: string, companyId: number) {
    try {
      return await db.query.customers.findFirst({
        where: and(
          sql`(${customers.phone} = ${phone} OR ${customers.alternatePhone} = ${phone})`,
          eq(customers.companyId, companyId),
          eq(customers.isActive, true)
        ),
      });
    } catch (error) {
      console.error('Error finding customer by phone:', error);
      return null;
    }
  }

  // Create lead from email/SMS
  async createLead(data: {
    companyId: number;
    email?: string;
    phone?: string;
    source: string;
    initialMessage?: string;
    subject?: string;
  }) {
    try {
      // Extract name from email if available
      let firstName = 'Unknown';
      let lastName = 'Lead';
      
      if (data.email) {
        const emailParts = data.email.split('@')[0].split('.');
        if (emailParts.length >= 2) {
          firstName = emailParts[0];
          lastName = emailParts[1];
        } else {
          firstName = emailParts[0];
        }
      }

      const [customer] = await db.insert(customers).values({
        companyId: data.companyId,
        firstName,
        lastName,
        email: data.email,
        phone: data.phone,
        source: data.source,
        flowState: 'initial_contact',
        notes: data.initialMessage ? `Initial message: ${data.initialMessage}` : undefined,
      }).returning();

      return customer;
    } catch (error) {
      console.error('Error creating lead:', error);
      throw new Error('Failed to create lead');
    }
  }

  // Get customer analytics
  async getCustomerAnalytics(companyId: number): Promise<CustomerAnalytics> {
    try {
      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

      // Total customers
      const [{ totalCustomers }] = await db
        .select({ totalCustomers: sql<number>`count(*)` })
        .from(customers)
        .where(and(eq(customers.companyId, companyId), eq(customers.isActive, true)));

      // New customers this month
      const [{ newCustomersThisMonth }] = await db
        .select({ newCustomersThisMonth: sql<number>`count(*)` })
        .from(customers)
        .where(and(
          eq(customers.companyId, companyId),
          eq(customers.isActive, true),
          sql`${customers.createdAt} >= ${startOfMonth}`
        ));

      // Active customers (had service in last 12 months)
      const twelveMonthsAgo = new Date();
      twelveMonthsAgo.setMonth(twelveMonthsAgo.getMonth() - 12);
      
      const [{ activeCustomers }] = await db
        .select({ activeCustomers: sql<number>`count(*)` })
        .from(customers)
        .where(and(
          eq(customers.companyId, companyId),
          eq(customers.isActive, true),
          sql`${customers.lastServiceDate} >= ${twelveMonthsAgo}`
        ));

      // Average health score
      const [{ averageHealthScore }] = await db
        .select({ averageHealthScore: sql<number>`avg(${customers.healthScore})` })
        .from(customers)
        .where(and(eq(customers.companyId, companyId), eq(customers.isActive, true)));

      // Average customer value
      const [{ averageCustomerValue }] = await db
        .select({ averageCustomerValue: sql<number>`avg(${customers.customerValue})` })
        .from(customers)
        .where(and(eq(customers.companyId, companyId), eq(customers.isActive, true)));

      // Churn risk distribution
      const churnRiskData = await db
        .select({
          risk: sql<string>`
            CASE 
              WHEN ${customers.churnRisk} < 30 THEN 'low'
              WHEN ${customers.churnRisk} < 70 THEN 'medium'
              ELSE 'high'
            END
          `,
          count: sql<number>`count(*)`
        })
        .from(customers)
        .where(and(eq(customers.companyId, companyId), eq(customers.isActive, true)))
        .groupBy(sql`
          CASE 
            WHEN ${customers.churnRisk} < 30 THEN 'low'
            WHEN ${customers.churnRisk} < 70 THEN 'medium'
            ELSE 'high'
          END
        `);

      const churnRiskDistribution = {
        low: 0,
        medium: 0,
        high: 0,
      };

      churnRiskData.forEach(item => {
        churnRiskDistribution[item.risk as keyof typeof churnRiskDistribution] = item.count;
      });

      // Flow state distribution
      const flowStateData = await db
        .select({
          flowState: customers.flowState,
          count: sql<number>`count(*)`
        })
        .from(customers)
        .where(and(eq(customers.companyId, companyId), eq(customers.isActive, true)))
        .groupBy(customers.flowState);

      const flowStateDistribution: Record<string, number> = {};
      flowStateData.forEach(item => {
        flowStateDistribution[item.flowState] = item.count;
      });

      // Top cities
      const topCities = await db
        .select({
          city: customers.city,
          count: sql<number>`count(*)`
        })
        .from(customers)
        .where(and(
          eq(customers.companyId, companyId),
          eq(customers.isActive, true),
          sql`${customers.city} IS NOT NULL AND ${customers.city} != ''`
        ))
        .groupBy(customers.city)
        .orderBy(sql`count(*) DESC`)
        .limit(10);

      return {
        totalCustomers,
        newCustomersThisMonth,
        activeCustomers,
        averageHealthScore: Math.round(averageHealthScore || 0),
        averageCustomerValue: Number(averageCustomerValue || 0),
        churnRiskDistribution,
        flowStateDistribution,
        topCities: topCities.map(item => ({ city: item.city || 'Unknown', count: item.count })),
      };
    } catch (error) {
      console.error('Error fetching customer analytics:', error);
      throw new Error('Failed to fetch customer analytics');
    }
  }

  // Update customer health score (AI-powered)
  async updateHealthScore(customerId: number, companyId: number) {
    try {
      const customer = await this.getCustomerById(customerId, companyId);
      
      // Calculate health score based on various factors
      let healthScore = 100;
      
      // Recent service activity
      const lastServiceDate = customer.lastServiceDate;
      if (lastServiceDate) {
        const daysSinceLastService = Math.floor(
          (Date.now() - lastServiceDate.getTime()) / (1000 * 60 * 60 * 24)
        );
        if (daysSinceLastService > 365) healthScore -= 30;
        else if (daysSinceLastService > 180) healthScore -= 15;
      } else {
        healthScore -= 20; // No service history
      }

      // Communication sentiment
      const recentCommunications = customer.communications.slice(0, 5);
      const avgSentiment = recentCommunications.reduce((sum, comm) => 
        sum + (Number(comm.sentimentScore) || 0), 0) / recentCommunications.length;
      
      if (avgSentiment < -0.5) healthScore -= 25;
      else if (avgSentiment < 0) healthScore -= 10;
      else if (avgSentiment > 0.5) healthScore += 5;

      // Payment history
      const overdueInvoices = customer.invoices.filter(inv => 
        inv.status === 'overdue'
      ).length;
      healthScore -= overdueInvoices * 15;

      // Ensure score is between 0 and 100
      healthScore = Math.max(0, Math.min(100, healthScore));

      // Update customer
      await this.updateCustomer(customerId, companyId, { healthScore });

      return healthScore;
    } catch (error) {
      console.error('Error updating health score:', error);
      throw new Error('Failed to update health score');
    }
  }
}

export const customerService = new CustomerService();