import { eq, and, desc } from 'drizzle-orm';
import { db } from '../libs/db';
import {
  calendarConnections,
  calendarEvents,
  calendarSyncLogs,
  customerCalendarInsights,
  type CalendarConnection,
  type CalendarEvent,
  type NewCalendarEvent,
  type NewCalendarSyncLog,
  SyncStatus,
  SyncType,
  LogStatus,
} from '../models/calendar';
import { microsoftGraphService, type CalendarEventData } from './microsoft-graph';
import { calendarAIAnalysisService } from './calendar-ai-analysis';
import { tokenEncryptionService } from './token-encryption';

export class CalendarSyncService {
  /**
   * Sync calendar events for a specific connection
   */
  async syncCalendarEvents(connectionId: string, syncType: 'full' | 'incremental' = 'incremental'): Promise<void> {
    const startTime = Date.now();
    let syncLog: NewCalendarSyncLog;

    try {
      // Get the calendar connection
      const connection = await db
        .select()
        .from(calendarConnections)
        .where(eq(calendarConnections.id, connectionId))
        .limit(1);

      if (!connection.length || !connection[0].isActive) {
        throw new Error('Calendar connection not found or inactive');
      }

      const conn = connection[0];

      // Create sync log
      syncLog = {
        connectionId,
        syncType: syncType === 'full' ? SyncType.FULL : SyncType.INCREMENTAL,
        status: LogStatus.STARTED,
        eventsProcessed: 0,
        eventsCreated: 0,
        eventsUpdated: 0,
        eventsDeleted: 0,
      };

      const [logResult] = await db.insert(calendarSyncLogs).values(syncLog).returning();
      const logId = logResult.id;

      // Update connection status
      await db
        .update(calendarConnections)
        .set({ syncStatus: SyncStatus.SYNCING })
        .where(eq(calendarConnections.id, connectionId));

      // Decrypt access token
      const accessToken = await tokenEncryptionService.decrypt(conn.accessTokenEncrypted!);

      // Check if token needs refresh
      const now = new Date();
      if (conn.expiresAt && conn.expiresAt <= now) {
        await this.refreshConnectionToken(connectionId);
        // Re-fetch connection with new token
        const refreshedConnection = await db
          .select()
          .from(calendarConnections)
          .where(eq(calendarConnections.id, connectionId))
          .limit(1);
        
        if (!refreshedConnection.length) {
          throw new Error('Failed to refresh connection token');
        }
      }

      // Fetch calendar events
      let events: CalendarEventData[] = [];
      
      if (syncType === 'full') {
        // Full sync: get events from last 6 months to next 6 months
        const startDate = new Date();
        startDate.setMonth(startDate.getMonth() - 6);
        const endDate = new Date();
        endDate.setMonth(endDate.getMonth() + 6);
        
        events = await microsoftGraphService.getCalendarEvents(accessToken, startDate, endDate, 1000);
      } else {
        // Incremental sync: get events from last sync or last 30 days
        const lastSyncDate = conn.lastSyncAt || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        const futureDate = new Date();
        futureDate.setMonth(futureDate.getMonth() + 3);
        
        events = await microsoftGraphService.getCalendarEvents(accessToken, lastSyncDate, futureDate, 500);
      }

      // Process events
      const processResults = await this.processCalendarEvents(events, connectionId);

      // Update sync log
      const duration = Date.now() - startTime;
      await db
        .update(calendarSyncLogs)
        .set({
          status: LogStatus.COMPLETED,
          eventsProcessed: events.length,
          eventsCreated: processResults.created,
          eventsUpdated: processResults.updated,
          eventsDeleted: processResults.deleted,
          durationMs: duration,
          completedAt: new Date(),
        })
        .where(eq(calendarSyncLogs.id, logId));

      // Update connection status
      await db
        .update(calendarConnections)
        .set({
          syncStatus: SyncStatus.COMPLETED,
          lastSyncAt: new Date(),
          errorMessage: null,
        })
        .where(eq(calendarConnections.id, connectionId));

      console.log(`Calendar sync completed for connection ${connectionId}: ${processResults.created} created, ${processResults.updated} updated, ${processResults.deleted} deleted`);

    } catch (error) {
      console.error('Calendar sync error:', error);

      // Update sync log with error
      if (syncLog) {
        const duration = Date.now() - startTime;
        await db
          .update(calendarSyncLogs)
          .set({
            status: LogStatus.FAILED,
            errorMessage: error instanceof Error ? error.message : 'Unknown error',
            durationMs: duration,
            completedAt: new Date(),
          })
          .where(eq(calendarSyncLogs.connectionId, connectionId));
      }

      // Update connection status
      await db
        .update(calendarConnections)
        .set({
          syncStatus: SyncStatus.ERROR,
          errorMessage: error instanceof Error ? error.message : 'Unknown error',
        })
        .where(eq(calendarConnections.id, connectionId));

      throw error;
    }
  }

  /**
   * Process calendar events: analyze, match customers, and store
   */
  private async processCalendarEvents(
    events: CalendarEventData[],
    connectionId: string
  ): Promise<{ created: number; updated: number; deleted: number }> {
    let created = 0;
    let updated = 0;
    let deleted = 0;

    for (const event of events) {
      try {
        // Check if event already exists
        const existingEvent = await db
          .select()
          .from(calendarEvents)
          .where(eq(calendarEvents.microsoftEventId, event.id))
          .limit(1);

        // Analyze event with AI
        const analysis = await calendarAIAnalysisService.analyzeCalendarEvent(event);

        // Try to match with existing customer
        const customerId = await this.matchCustomer(analysis.extractedCustomerInfo);

        // Prepare event data
        const eventData: Partial<NewCalendarEvent> = {
          microsoftEventId: event.id,
          connectionId,
          customerId,
          subject: event.subject,
          bodyContent: event.body?.content,
          bodyContentType: event.body?.contentType || 'text',
          startTime: event.start?.dateTime ? new Date(event.start.dateTime) : null,
          endTime: event.end?.dateTime ? new Date(event.end.dateTime) : null,
          location: event.location?.displayName,
          attendees: event.attendees || [],
          organizer: event.organizer || {},
          isAllDay: event.isAllDay || false,
          isCancelled: event.isCancelled || false,
          importance: event.importance || 'normal',
          sensitivity: event.sensitivity || 'normal',
          showAs: event.showAs || 'busy',
          aiAnalysis: analysis,
          confidenceScore: analysis.confidenceScore.toString(),
          eventType: analysis.eventType,
          priority: analysis.priority,
          extractedCustomerInfo: analysis.extractedCustomerInfo,
          sentimentScore: analysis.sentimentScore.toString(),
          actionItems: analysis.actionItems,
          lastModified: event.lastModifiedDateTime ? new Date(event.lastModifiedDateTime) : null,
          changeKey: event.changeKey,
        };

        if (existingEvent.length > 0) {
          // Update existing event
          await db
            .update(calendarEvents)
            .set(eventData)
            .where(eq(calendarEvents.id, existingEvent[0].id));
          updated++;
        } else {
          // Create new event
          await db.insert(calendarEvents).values(eventData as NewCalendarEvent);
          created++;
        }

        // Update customer insights if customer matched
        if (customerId) {
          await this.updateCustomerInsights(customerId);
        }

      } catch (error) {
        console.error(`Error processing event ${event.id}:`, error);
        // Continue processing other events
      }
    }

    return { created, updated, deleted };
  }

  /**
   * Match calendar event with existing customer using AI-extracted information
   */
  private async matchCustomer(extractedInfo: any): Promise<string | null> {
    if (!extractedInfo.emailAddresses?.length && !extractedInfo.customerName && !extractedInfo.companyName) {
      return null;
    }

    try {
      // Import customers table and fuzzy matching utilities
      const { customers } = await import('../models/Schema');
      const { or, ilike, sql } = await import('drizzle-orm');

      // Strategy 1: Exact email match (highest confidence)
      if (extractedInfo.emailAddresses?.length > 0) {
        for (const email of extractedInfo.emailAddresses) {
          const emailMatch = await db
            .select({ id: customers.id })
            .from(customers)
            .where(ilike(customers.email, email))
            .limit(1);

          if (emailMatch.length > 0) {
            console.log(`Customer matched by email: ${email} -> ${emailMatch[0].id}`);
            return emailMatch[0].id;
          }
        }
      }

      // Strategy 2: Company name match (medium confidence)
      if (extractedInfo.companyName) {
        const companyMatch = await db
          .select({ id: customers.id })
          .from(customers)
          .where(ilike(customers.company, `%${extractedInfo.companyName}%`))
          .limit(1);

        if (companyMatch.length > 0) {
          console.log(`Customer matched by company: ${extractedInfo.companyName} -> ${companyMatch[0].id}`);
          return companyMatch[0].id;
        }
      }

      // Strategy 3: Customer name fuzzy match (lower confidence)
      if (extractedInfo.customerName) {
        // Split name into parts for better matching
        const nameParts = extractedInfo.customerName.toLowerCase().split(' ').filter(part => part.length > 2);

        if (nameParts.length > 0) {
          const nameConditions = nameParts.map(part =>
            or(
              ilike(customers.firstName, `%${part}%`),
              ilike(customers.lastName, `%${part}%`),
              ilike(customers.name, `%${part}%`)
            )
          );

          const nameMatch = await db
            .select({ id: customers.id, firstName: customers.firstName, lastName: customers.lastName, name: customers.name })
            .from(customers)
            .where(or(...nameConditions))
            .limit(5);

          if (nameMatch.length > 0) {
            // Use simple scoring to find best match
            let bestMatch = nameMatch[0];
            let bestScore = 0;

            for (const match of nameMatch) {
              const fullName = `${match.firstName || ''} ${match.lastName || ''}`.trim() || match.name || '';
              const score = this.calculateNameSimilarity(extractedInfo.customerName, fullName);

              if (score > bestScore && score > 0.6) { // Minimum 60% similarity
                bestScore = score;
                bestMatch = match;
              }
            }

            if (bestScore > 0.6) {
              console.log(`Customer matched by name: ${extractedInfo.customerName} -> ${bestMatch.id} (score: ${bestScore})`);
              return bestMatch.id;
            }
          }
        }
      }

      // Strategy 4: Phone number match (if available)
      if (extractedInfo.phoneNumbers?.length > 0) {
        for (const phone of extractedInfo.phoneNumbers) {
          // Normalize phone number (remove spaces, dashes, parentheses)
          const normalizedPhone = phone.replace(/[\s\-\(\)]/g, '');

          const phoneMatch = await db
            .select({ id: customers.id })
            .from(customers)
            .where(ilike(customers.phone, `%${normalizedPhone}%`))
            .limit(1);

          if (phoneMatch.length > 0) {
            console.log(`Customer matched by phone: ${phone} -> ${phoneMatch[0].id}`);
            return phoneMatch[0].id;
          }
        }
      }

      console.log('No customer match found for extracted info:', extractedInfo);
      return null;

    } catch (error) {
      console.error('Error matching customer:', error);
      return null;
    }
  }

  /**
   * Calculate similarity between two names using simple string matching
   */
  private calculateNameSimilarity(name1: string, name2: string): number {
    const normalize = (str: string) => str.toLowerCase().replace(/[^a-z\s]/g, '').trim();
    const n1 = normalize(name1);
    const n2 = normalize(name2);

    if (n1 === n2) return 1.0;
    if (n1.length === 0 || n2.length === 0) return 0;

    // Simple word-based similarity
    const words1 = n1.split(' ').filter(w => w.length > 1);
    const words2 = n2.split(' ').filter(w => w.length > 1);

    if (words1.length === 0 || words2.length === 0) return 0;

    let matches = 0;
    for (const word1 of words1) {
      for (const word2 of words2) {
        if (word1 === word2 || word1.includes(word2) || word2.includes(word1)) {
          matches++;
          break;
        }
      }
    }

    return matches / Math.max(words1.length, words2.length);
  }

  /**
   * Update customer calendar insights with comprehensive analysis
   */
  private async updateCustomerInsights(customerId: string): Promise<void> {
    try {
      // Get all calendar events for this customer
      const customerEvents = await db
        .select()
        .from(calendarEvents)
        .where(eq(calendarEvents.customerId, customerId))
        .orderBy(desc(calendarEvents.startTime));

      if (customerEvents.length === 0) {
        console.log(`No events found for customer ${customerId}, skipping insights update`);
        return;
      }

      const now = new Date();
      const pastEvents = customerEvents.filter(event =>
        event.startTime && new Date(event.startTime) <= now
      );
      const futureEvents = customerEvents.filter(event =>
        event.startTime && new Date(event.startTime) > now
      );

      // 1. Calculate meeting frequency
      let meetingFrequencyDays = 0;
      if (pastEvents.length > 1) {
        const sortedPastEvents = pastEvents.sort((a, b) =>
          new Date(a.startTime!).getTime() - new Date(b.startTime!).getTime()
        );
        const firstEvent = new Date(sortedPastEvents[0].startTime!);
        const lastEvent = new Date(sortedPastEvents[sortedPastEvents.length - 1].startTime!);
        const daysDiff = (lastEvent.getTime() - firstEvent.getTime()) / (1000 * 60 * 60 * 24);
        meetingFrequencyDays = daysDiff / (pastEvents.length - 1);
      }

      // 2. Analyze preferred meeting times (hour of day)
      const hourCounts: Record<number, number> = {};
      customerEvents.forEach(event => {
        if (event.startTime) {
          const hour = new Date(event.startTime).getHours();
          hourCounts[hour] = (hourCounts[hour] || 0) + 1;
        }
      });

      // 3. Identify common meeting types
      const typeCounts: Record<string, number> = {};
      customerEvents.forEach(event => {
        const type = event.eventType || 'other';
        typeCounts[type] = (typeCounts[type] || 0) + 1;
      });
      const commonMeetingTypes = Object.entries(typeCounts)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 5)
        .map(([type]) => type);

      // 4. Calculate engagement score based on multiple factors
      const avgConfidence = customerEvents.reduce((sum, event) =>
        sum + parseFloat(event.confidenceScore || '0'), 0) / customerEvents.length;

      const avgSentiment = customerEvents.reduce((sum, event) =>
        sum + parseFloat(event.sentimentScore || '0'), 0) / customerEvents.length;

      const recentActivity = pastEvents.filter(event =>
        event.startTime && new Date(event.startTime) > new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
      ).length;

      const avgPriority = customerEvents.reduce((sum, event) =>
        sum + (event.priority || 3), 0) / customerEvents.length;

      // Engagement score formula (0.0 - 1.0)
      const engagementScore = Math.min(1.0,
        (avgConfidence * 0.25) +                    // AI confidence in analysis
        ((avgSentiment + 1) / 2 * 0.25) +          // Sentiment (normalized to 0-1)
        (Math.min(recentActivity / 10, 1) * 0.3) + // Recent activity
        ((avgPriority / 5) * 0.2)                  // Average priority
      );

      // 5. Prepare insights data
      const insightsData = {
        customerId,
        totalMeetings: customerEvents.length,
        lastMeetingDate: pastEvents.length > 0 ? new Date(pastEvents[0].startTime!) : null,
        nextMeetingDate: futureEvents.length > 0 ? new Date(futureEvents[0].startTime!) : null,
        meetingFrequencyDays: meetingFrequencyDays.toString(),
        preferredMeetingTimes: hourCounts,
        commonMeetingTypes,
        engagementScore: engagementScore.toString(),
        lastCalculatedAt: new Date(),
      };

      // 6. Upsert insights record
      const existingInsights = await db
        .select()
        .from(customerCalendarInsights)
        .where(eq(customerCalendarInsights.customerId, customerId))
        .limit(1);

      if (existingInsights.length > 0) {
        await db
          .update(customerCalendarInsights)
          .set(insightsData)
          .where(eq(customerCalendarInsights.customerId, customerId));
      } else {
        await db
          .insert(customerCalendarInsights)
          .values(insightsData);
      }

      console.log(`Updated calendar insights for customer ${customerId}: ${customerEvents.length} events, engagement: ${Math.round(engagementScore * 100)}%`);

    } catch (error) {
      console.error(`Error updating customer insights for ${customerId}:`, error);
    }
  }

  /**
   * Refresh connection token
   */
  private async refreshConnectionToken(connectionId: string): Promise<void> {
    const connection = await db
      .select()
      .from(calendarConnections)
      .where(eq(calendarConnections.id, connectionId))
      .limit(1);

    if (!connection.length || !connection[0].refreshTokenEncrypted) {
      throw new Error('Cannot refresh token: connection or refresh token not found');
    }

    const refreshToken = await tokenEncryptionService.decrypt(connection[0].refreshTokenEncrypted);
    const tokenData = await microsoftGraphService.refreshToken(refreshToken);

    // Encrypt new tokens
    const encryptedAccessToken = await tokenEncryptionService.encrypt(tokenData.accessToken);
    const encryptedRefreshToken = tokenData.refreshToken 
      ? await tokenEncryptionService.encrypt(tokenData.refreshToken)
      : connection[0].refreshTokenEncrypted;

    // Update connection with new tokens
    await db
      .update(calendarConnections)
      .set({
        accessTokenEncrypted: encryptedAccessToken,
        refreshTokenEncrypted: encryptedRefreshToken,
        expiresAt: tokenData.expiresOn || null,
      })
      .where(eq(calendarConnections.id, connectionId));
  }

  /**
   * Sync all active connections
   */
  async syncAllConnections(): Promise<void> {
    const activeConnections = await db
      .select()
      .from(calendarConnections)
      .where(eq(calendarConnections.isActive, true));

    const syncPromises = activeConnections.map(connection =>
      this.syncCalendarEvents(connection.id, 'incremental').catch(error => {
        console.error(`Failed to sync connection ${connection.id}:`, error);
      })
    );

    await Promise.all(syncPromises);
  }

  /**
   * Get sync status for a connection
   */
  async getSyncStatus(connectionId: string) {
    const connection = await db
      .select()
      .from(calendarConnections)
      .where(eq(calendarConnections.id, connectionId))
      .limit(1);

    if (!connection.length) {
      throw new Error('Connection not found');
    }

    const recentLogs = await db
      .select()
      .from(calendarSyncLogs)
      .where(eq(calendarSyncLogs.connectionId, connectionId))
      .orderBy(desc(calendarSyncLogs.startedAt))
      .limit(5);

    return {
      connection: connection[0],
      recentLogs,
    };
  }
}

// Singleton instance
export const calendarSyncService = new CalendarSyncService();
