// 🔧 HVAC Service Ticket Service - PEŁNA MOC WIATRU! ⚡
// Complete service ticket management with AI-powered assignment

import { eq, and, desc, asc, like, sql, inArray } from 'drizzle-orm';
import { db } from '@/libs/DB';
import { 
  serviceTickets, 
  customers,
  technicians,
  buildings,
  communications,
  type serviceTicketStatusEnum,
  type serviceTicketPriorityEnum 
} from '@/models/Schema';

export interface CreateServiceTicketData {
  companyId: number;
  customerId: number;
  buildingId?: number;
  title: string;
  description?: string;
  priority?: typeof serviceTicketPriorityEnum.enumValues[number];
  serviceType?: string;
  equipmentType?: string;
  problemCategory?: string;
  scheduledDate?: Date;
  estimatedDuration?: number;
  estimatedCost?: number;
}

export interface UpdateServiceTicketData extends Partial<CreateServiceTicketData> {
  status?: typeof serviceTicketStatusEnum.enumValues[number];
  assignedTechnicianId?: number;
  actualStartTime?: Date;
  actualEndTime?: Date;
  actualCost?: number;
  laborHours?: number;
  workPerformed?: string;
  partsUsed?: any[];
  notes?: string;
  internalNotes?: string;
  customerRating?: number;
  customerFeedback?: string;
  beforePhotos?: string[];
  afterPhotos?: string[];
  attachments?: string[];
}

export interface ServiceTicketFilters {
  companyId: number;
  status?: typeof serviceTicketStatusEnum.enumValues[number][];
  priority?: typeof serviceTicketPriorityEnum.enumValues[number][];
  assignedTechnicianId?: number;
  customerId?: number;
  serviceType?: string;
  dateFrom?: Date;
  dateTo?: Date;
  search?: string;
}

export class ServiceTicketService {
  
  // Generate unique ticket number
  private async generateTicketNumber(companyId: number): Promise<string> {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    
    // Get count of tickets created today
    const [{ count }] = await db
      .select({ count: sql<number>`count(*)` })
      .from(serviceTickets)
      .where(and(
        eq(serviceTickets.companyId, companyId),
        sql`DATE(${serviceTickets.createdAt}) = CURRENT_DATE`
      ));

    const sequence = String(count + 1).padStart(3, '0');
    return `ST-${year}${month}${day}-${sequence}`;
  }

  // Create new service ticket
  async createServiceTicket(data: CreateServiceTicketData) {
    try {
      const ticketNumber = await this.generateTicketNumber(data.companyId);
      
      const [ticket] = await db.insert(serviceTickets).values({
        ...data,
        ticketNumber,
        partsUsed: data.partsUsed ? JSON.stringify(data.partsUsed) : null,
      }).returning();

      // Auto-assign technician if not specified
      if (!data.assignedTechnicianId && data.priority && ['high', 'urgent', 'emergency'].includes(data.priority)) {
        await this.autoAssignTechnician(ticket.id, data.companyId);
      }

      return ticket;
    } catch (error) {
      console.error('Error creating service ticket:', error);
      throw new Error('Failed to create service ticket');
    }
  }

  // Get service ticket by ID
  async getServiceTicketById(id: number, companyId: number) {
    try {
      const ticket = await db.query.serviceTickets.findFirst({
        where: and(eq(serviceTickets.id, id), eq(serviceTickets.companyId, companyId)),
        with: {
          customer: true,
          building: true,
          assignedTechnician: true,
          communications: {
            orderBy: desc(communications.createdAt),
            limit: 10,
          },
        }
      });

      if (!ticket) {
        throw new Error('Service ticket not found');
      }

      return ticket;
    } catch (error) {
      console.error('Error fetching service ticket:', error);
      throw new Error('Failed to fetch service ticket');
    }
  }

  // Get service tickets with filters
  async getServiceTickets(filters: ServiceTicketFilters, page = 1, limit = 20) {
    try {
      const offset = (page - 1) * limit;
      const conditions = [eq(serviceTickets.companyId, filters.companyId)];

      // Apply filters
      if (filters.status && filters.status.length > 0) {
        conditions.push(inArray(serviceTickets.status, filters.status));
      }

      if (filters.priority && filters.priority.length > 0) {
        conditions.push(inArray(serviceTickets.priority, filters.priority));
      }

      if (filters.assignedTechnicianId) {
        conditions.push(eq(serviceTickets.assignedTechnicianId, filters.assignedTechnicianId));
      }

      if (filters.customerId) {
        conditions.push(eq(serviceTickets.customerId, filters.customerId));
      }

      if (filters.serviceType) {
        conditions.push(like(serviceTickets.serviceType, `%${filters.serviceType}%`));
      }

      if (filters.dateFrom) {
        conditions.push(sql`${serviceTickets.scheduledDate} >= ${filters.dateFrom}`);
      }

      if (filters.dateTo) {
        conditions.push(sql`${serviceTickets.scheduledDate} <= ${filters.dateTo}`);
      }

      if (filters.search) {
        conditions.push(
          sql`(${serviceTickets.title} ILIKE ${`%${filters.search}%`} OR 
               ${serviceTickets.description} ILIKE ${`%${filters.search}%`} OR 
               ${serviceTickets.ticketNumber} ILIKE ${`%${filters.search}%`})`
        );
      }

      const whereClause = conditions.length > 1 ? and(...conditions) : conditions[0];

      // Get tickets with pagination
      const ticketsList = await db.query.serviceTickets.findMany({
        where: whereClause,
        orderBy: [desc(serviceTickets.priority), desc(serviceTickets.createdAt)],
        limit,
        offset,
        with: {
          customer: true,
          assignedTechnician: true,
          building: true,
        }
      });

      // Get total count
      const [{ count }] = await db
        .select({ count: sql<number>`count(*)` })
        .from(serviceTickets)
        .where(whereClause);

      return {
        tickets: ticketsList,
        total: count,
        page,
        limit,
        totalPages: Math.ceil(count / limit),
      };
    } catch (error) {
      console.error('Error fetching service tickets:', error);
      throw new Error('Failed to fetch service tickets');
    }
  }

  // Update service ticket
  async updateServiceTicket(id: number, companyId: number, data: UpdateServiceTicketData) {
    try {
      const [ticket] = await db
        .update(serviceTickets)
        .set({
          ...data,
          partsUsed: data.partsUsed ? JSON.stringify(data.partsUsed) : undefined,
          beforePhotos: data.beforePhotos ? JSON.stringify(data.beforePhotos) : undefined,
          afterPhotos: data.afterPhotos ? JSON.stringify(data.afterPhotos) : undefined,
          attachments: data.attachments ? JSON.stringify(data.attachments) : undefined,
          updatedAt: new Date(),
        })
        .where(and(eq(serviceTickets.id, id), eq(serviceTickets.companyId, companyId)))
        .returning();

      if (!ticket) {
        throw new Error('Service ticket not found');
      }

      return ticket;
    } catch (error) {
      console.error('Error updating service ticket:', error);
      throw new Error('Failed to update service ticket');
    }
  }

  // Auto-assign technician based on AI algorithm
  async autoAssignTechnician(ticketId: number, companyId: number) {
    try {
      const ticket = await this.getServiceTicketById(ticketId, companyId);
      
      // Get available technicians
      const availableTechnicians = await db.query.technicians.findMany({
        where: and(
          eq(technicians.companyId, companyId),
          eq(technicians.isActive, true),
          inArray(technicians.status, ['available', 'break'])
        ),
      });

      if (availableTechnicians.length === 0) {
        throw new Error('No available technicians');
      }

      // AI-powered assignment algorithm
      let bestTechnician = availableTechnicians[0];
      let bestScore = 0;

      for (const tech of availableTechnicians) {
        let score = 0;

        // Check specializations match
        const specializations = tech.specializations as string[] || [];
        if (ticket.equipmentType && specializations.includes(ticket.equipmentType)) {
          score += 30;
        }
        if (ticket.serviceType && specializations.includes(ticket.serviceType)) {
          score += 20;
        }

        // Rating bonus
        score += Number(tech.averageRating) * 10;

        // Workload penalty (fewer current jobs = higher score)
        const currentJobs = await db
          .select({ count: sql<number>`count(*)` })
          .from(serviceTickets)
          .where(and(
            eq(serviceTickets.assignedTechnicianId, tech.id),
            inArray(serviceTickets.status, ['assigned', 'in_progress'])
          ));
        
        score -= currentJobs[0].count * 5;

        // Location proximity (if building has coordinates)
        if (ticket.building?.latitude && ticket.building?.longitude && 
            tech.currentLatitude && tech.currentLongitude) {
          const distance = this.calculateDistance(
            Number(ticket.building.latitude),
            Number(ticket.building.longitude),
            Number(tech.currentLatitude),
            Number(tech.currentLongitude)
          );
          score += Math.max(0, 50 - distance); // Closer = higher score
        }

        if (score > bestScore) {
          bestScore = score;
          bestTechnician = tech;
        }
      }

      // Assign the best technician
      await this.updateServiceTicket(ticketId, companyId, {
        assignedTechnicianId: bestTechnician.id,
        status: 'assigned',
      });

      return bestTechnician;
    } catch (error) {
      console.error('Error auto-assigning technician:', error);
      throw new Error('Failed to auto-assign technician');
    }
  }

  // Calculate distance between two coordinates (Haversine formula)
  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) * 
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI/180);
  }

  // Get service ticket analytics
  async getServiceTicketAnalytics(companyId: number) {
    try {
      // Total tickets
      const [{ totalTickets }] = await db
        .select({ totalTickets: sql<number>`count(*)` })
        .from(serviceTickets)
        .where(eq(serviceTickets.companyId, companyId));

      // Tickets by status
      const statusData = await db
        .select({
          status: serviceTickets.status,
          count: sql<number>`count(*)`
        })
        .from(serviceTickets)
        .where(eq(serviceTickets.companyId, companyId))
        .groupBy(serviceTickets.status);

      // Tickets by priority
      const priorityData = await db
        .select({
          priority: serviceTickets.priority,
          count: sql<number>`count(*)`
        })
        .from(serviceTickets)
        .where(eq(serviceTickets.companyId, companyId))
        .groupBy(serviceTickets.priority);

      // Average completion time
      const [{ avgCompletionTime }] = await db
        .select({ 
          avgCompletionTime: sql<number>`
            AVG(EXTRACT(EPOCH FROM (${serviceTickets.actualEndTime} - ${serviceTickets.actualStartTime})) / 3600)
          `
        })
        .from(serviceTickets)
        .where(and(
          eq(serviceTickets.companyId, companyId),
          eq(serviceTickets.status, 'completed'),
          sql`${serviceTickets.actualStartTime} IS NOT NULL`,
          sql`${serviceTickets.actualEndTime} IS NOT NULL`
        ));

      // Average customer rating
      const [{ avgRating }] = await db
        .select({ avgRating: sql<number>`avg(${serviceTickets.customerRating})` })
        .from(serviceTickets)
        .where(and(
          eq(serviceTickets.companyId, companyId),
          sql`${serviceTickets.customerRating} IS NOT NULL`
        ));

      // Revenue this month
      const startOfMonth = new Date();
      startOfMonth.setDate(1);
      startOfMonth.setHours(0, 0, 0, 0);

      const [{ monthlyRevenue }] = await db
        .select({ monthlyRevenue: sql<number>`sum(${serviceTickets.actualCost})` })
        .from(serviceTickets)
        .where(and(
          eq(serviceTickets.companyId, companyId),
          eq(serviceTickets.status, 'completed'),
          sql`${serviceTickets.actualEndTime} >= ${startOfMonth}`
        ));

      return {
        totalTickets,
        statusDistribution: statusData.reduce((acc, item) => {
          acc[item.status] = item.count;
          return acc;
        }, {} as Record<string, number>),
        priorityDistribution: priorityData.reduce((acc, item) => {
          acc[item.priority] = item.count;
          return acc;
        }, {} as Record<string, number>),
        averageCompletionTime: Number(avgCompletionTime || 0),
        averageCustomerRating: Number(avgRating || 0),
        monthlyRevenue: Number(monthlyRevenue || 0),
      };
    } catch (error) {
      console.error('Error fetching service ticket analytics:', error);
      throw new Error('Failed to fetch service ticket analytics');
    }
  }

  // Get technician workload
  async getTechnicianWorkload(companyId: number) {
    try {
      const workloadData = await db
        .select({
          technicianId: technicians.id,
          firstName: technicians.firstName,
          lastName: technicians.lastName,
          activeTickets: sql<number>`count(${serviceTickets.id})`,
          avgRating: technicians.averageRating,
          status: technicians.status,
        })
        .from(technicians)
        .leftJoin(serviceTickets, and(
          eq(serviceTickets.assignedTechnicianId, technicians.id),
          inArray(serviceTickets.status, ['assigned', 'in_progress'])
        ))
        .where(and(
          eq(technicians.companyId, companyId),
          eq(technicians.isActive, true)
        ))
        .groupBy(technicians.id, technicians.firstName, technicians.lastName, 
                 technicians.averageRating, technicians.status);

      return workloadData;
    } catch (error) {
      console.error('Error fetching technician workload:', error);
      throw new Error('Failed to fetch technician workload');
    }
  }
}

export const serviceTicketService = new ServiceTicketService();