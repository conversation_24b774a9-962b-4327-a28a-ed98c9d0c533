// 🎤 Voice Transcription Service - PEŁNA MOC WIATRU! ⚡
// AI-powered voice transcription with OpenAI Whisper integration

import OpenAI from 'openai';
import { eq, and, desc, sql } from 'drizzle-orm';
import { db } from '@/libs/DB';
import {
  transcriptions,
  communications,
  serviceTickets,
  customers,
  type serviceTicketPriorityEnum
} from '@/models/Schema';
import { communicationService } from './communication.service';
import { serviceTicketService } from './serviceTicket.service';

// Initialize OpenAI
const openai = process.env.OPENAI_API_KEY ? new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
}) : null;

export interface TranscriptionData {
  companyId: number;
  customerId?: number;
  serviceTicketId?: number;
  audioUrl: string;
  context: 'customer_call' | 'technician_note' | 'voicemail' | 'field_recording';
  language?: string;
}

export interface TranscriptionAnalysis {
  transcript: string;
  confidence: number;
  duration: number;
  analysis: {
    sentiment: number;
    intent: string;
    urgencyLevel: number;
    keyPoints: string[];
    actionItems: string[];
    equipmentMentioned: string[];
    problemDescription?: string;
  };
  followUpRequired: boolean;
  suggestedActions: string[];
}

export interface VoiceAnalysisResult {
  transcriptionId: number;
  transcript: string;
  analysis: TranscriptionAnalysis['analysis'];
  shouldCreateTicket: boolean;
  shouldNotifyTechnician: boolean;
  priority: typeof serviceTicketPriorityEnum.enumValues[number];
}

export class TranscriptionService {
  
  // Transcribe audio file using OpenAI Whisper
  async transcribeAudio(data: TranscriptionData): Promise<VoiceAnalysisResult> {
    try {
      console.log('Starting transcription for:', data.audioUrl);
      
      if (!openai) {
        throw new Error('OpenAI not configured for transcription');
      }

      // Download and transcribe audio
      const transcriptionResult = await this.performTranscription(data.audioUrl, data.language);
      
      // Analyze transcript content
      const analysis = await this.analyzeTranscript(transcriptionResult.transcript, data.context);
      
      // Create transcription record
      const [transcription] = await db.insert(transcriptions).values({
        companyId: data.companyId,
        customerId: data.customerId,
        serviceTicketId: data.serviceTicketId,
        audioUrl: data.audioUrl,
        transcript: transcriptionResult.transcript,
        context: data.context,
        confidence: transcriptionResult.confidence,
        duration: transcriptionResult.duration,
        language: data.language || 'pl',
        analysis: JSON.stringify(analysis.analysis),
        intent: analysis.analysis.intent,
        sentimentScore: analysis.analysis.sentiment,
        urgencyLevel: this.mapUrgencyToPriority(analysis.analysis.urgencyLevel),
        isProcessed: true,
        followUpRequired: analysis.followUpRequired,
      }).returning();

      // Create communication record if it's a customer call
      if (data.context === 'customer_call' || data.context === 'voicemail') {
        await communicationService.createCommunication({
          companyId: data.companyId,
          customerId: data.customerId,
          serviceTicketId: data.serviceTicketId,
          type: 'phone',
          direction: 'inbound',
          subject: `Rozmowa telefoniczna - ${analysis.analysis.intent}`,
          content: transcriptionResult.transcript,
          transcriptionId: transcription.id,
        });
      }

      // Determine actions
      const shouldCreateTicket = this.shouldCreateServiceTicket(analysis);
      const shouldNotifyTechnician = analysis.analysis.urgencyLevel > 70;
      const priority = this.mapUrgencyToPriority(analysis.analysis.urgencyLevel);

      return {
        transcriptionId: transcription.id,
        transcript: transcriptionResult.transcript,
        analysis: analysis.analysis,
        shouldCreateTicket,
        shouldNotifyTechnician,
        priority,
      };
    } catch (error) {
      console.error('Error transcribing audio:', error);
      throw new Error('Failed to transcribe audio');
    }
  }

  // Perform actual transcription using Whisper API
  private async performTranscription(audioUrl: string, language = 'pl') {
    try {
      if (!openai) {
        throw new Error('OpenAI not configured');
      }

      // For demo purposes, we'll simulate the transcription
      // In production, you would download the audio file and send it to Whisper
      console.log('Transcribing audio from:', audioUrl);
      
      // Mock transcription result
      const mockTranscript = "Dzień dobry, mam problem z klimatyzacją. Urządzenie nie chłodzi i wydaje dziwne dźwięki. Czy mogliby Państwo przyjechać dzisiaj? To bardzo pilne, bo mamy dzieci w domu.";
      
      // In production, this would be:
      // const transcription = await openai.audio.transcriptions.create({
      //   file: audioFile,
      //   model: "whisper-1",
      //   language: language,
      //   response_format: "verbose_json",
      // });

      return {
        transcript: mockTranscript,
        confidence: 0.95,
        duration: 45, // seconds
      };
    } catch (error) {
      console.error('Error with Whisper API:', error);
      throw new Error('Failed to transcribe with Whisper API');
    }
  }

  // Analyze transcript content with AI
  private async analyzeTranscript(transcript: string, context: string): Promise<TranscriptionAnalysis> {
    try {
      if (!openai) {
        return this.basicTranscriptAnalysis(transcript, context);
      }

      const prompt = `
Analyze this HVAC service call transcript and provide a JSON response:

{
  "transcript": "${transcript}",
  "confidence": 0.95,
  "duration": 45,
  "analysis": {
    "sentiment": number (-1 to 1),
    "intent": string,
    "urgencyLevel": number (0-100),
    "keyPoints": string[],
    "actionItems": string[],
    "equipmentMentioned": string[],
    "problemDescription": string
  },
  "followUpRequired": boolean,
  "suggestedActions": string[]
}

Context: ${context}
Transcript: ${transcript}

Focus on HVAC-specific terminology and Polish language context. Identify equipment types, problem severity, and required actions.
`;

      const completion = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: "You are an AI assistant specialized in analyzing HVAC service call transcripts. Respond only with valid JSON."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 1000,
      });

      const response = completion.choices[0]?.message?.content;
      if (!response) {
        throw new Error('No response from OpenAI');
      }

      try {
        return JSON.parse(response);
      } catch (parseError) {
        console.error('Error parsing OpenAI response:', parseError);
        return this.basicTranscriptAnalysis(transcript, context);
      }
    } catch (error) {
      console.error('Error with AI analysis:', error);
      return this.basicTranscriptAnalysis(transcript, context);
    }
  }

  // Basic transcript analysis fallback
  private basicTranscriptAnalysis(transcript: string, context: string): TranscriptionAnalysis {
    const lowerTranscript = transcript.toLowerCase();
    
    // Sentiment analysis
    let sentiment = 0;
    const positiveWords = ['dziękuję', 'świetnie', 'dobrze', 'zadowolony'];
    const negativeWords = ['problem', 'awaria', 'nie działa', 'zepsuty', 'pilne', 'natychmiast'];
    
    positiveWords.forEach(word => {
      if (lowerTranscript.includes(word)) sentiment += 0.2;
    });
    
    negativeWords.forEach(word => {
      if (lowerTranscript.includes(word)) sentiment -= 0.3;
    });
    
    sentiment = Math.max(-1, Math.min(1, sentiment));

    // Urgency detection
    let urgencyLevel = 30;
    if (lowerTranscript.includes('pilne') || lowerTranscript.includes('natychmiast')) urgencyLevel += 40;
    if (lowerTranscript.includes('awaria') || lowerTranscript.includes('nie działa')) urgencyLevel += 30;
    if (lowerTranscript.includes('dzieci') || lowerTranscript.includes('starsi')) urgencyLevel += 20;
    if (lowerTranscript.includes('dzisiaj') || lowerTranscript.includes('teraz')) urgencyLevel += 15;
    
    urgencyLevel = Math.min(100, urgencyLevel);

    // Equipment detection
    const equipmentMentioned = [];
    if (lowerTranscript.includes('klimatyzacja') || lowerTranscript.includes('klimatyzator')) {
      equipmentMentioned.push('klimatyzacja');
    }
    if (lowerTranscript.includes('ogrzewanie') || lowerTranscript.includes('grzejnik')) {
      equipmentMentioned.push('ogrzewanie');
    }
    if (lowerTranscript.includes('wentylacja') || lowerTranscript.includes('wentylator')) {
      equipmentMentioned.push('wentylacja');
    }

    // Intent detection
    let intent = 'general_inquiry';
    if (lowerTranscript.includes('awaria') || lowerTranscript.includes('nie działa')) {
      intent = 'emergency_repair';
    } else if (lowerTranscript.includes('serwis') || lowerTranscript.includes('przegląd')) {
      intent = 'maintenance_request';
    } else if (lowerTranscript.includes('wycena') || lowerTranscript.includes('oferta')) {
      intent = 'quote_request';
    }

    // Key points extraction
    const keyPoints = [];
    if (equipmentMentioned.length > 0) {
      keyPoints.push(`Sprzęt: ${equipmentMentioned.join(', ')}`);
    }
    if (urgencyLevel > 70) {
      keyPoints.push('Wysoki priorytet');
    }
    if (lowerTranscript.includes('dźwięk') || lowerTranscript.includes('hałas')) {
      keyPoints.push('Problem z hałasem');
    }

    // Action items
    const actionItems = [];
    if (urgencyLevel > 70) {
      actionItems.push('Natychmiastowy kontakt z klientem');
      actionItems.push('Przydzielenie technika w trybie pilnym');
    } else {
      actionItems.push('Umówienie wizyty technicznej');
    }

    return {
      transcript,
      confidence: 0.85,
      duration: Math.floor(transcript.length / 10), // Rough estimate
      analysis: {
        sentiment,
        intent,
        urgencyLevel,
        keyPoints,
        actionItems,
        equipmentMentioned,
        problemDescription: transcript.length > 100 ? transcript.substring(0, 200) + '...' : transcript,
      },
      followUpRequired: urgencyLevel > 50 || sentiment < -0.2,
      suggestedActions: actionItems,
    };
  }

  // Map urgency level to priority enum
  private mapUrgencyToPriority(urgencyLevel: number): typeof serviceTicketPriorityEnum.enumValues[number] {
    if (urgencyLevel >= 90) return 'emergency';
    if (urgencyLevel >= 75) return 'urgent';
    if (urgencyLevel >= 60) return 'high';
    if (urgencyLevel >= 40) return 'normal';
    return 'low';
  }

  // Determine if service ticket should be created
  private shouldCreateServiceTicket(analysis: TranscriptionAnalysis): boolean {
    return (
      analysis.analysis.urgencyLevel > 60 ||
      analysis.analysis.intent.includes('repair') ||
      analysis.analysis.intent.includes('maintenance') ||
      analysis.analysis.equipmentMentioned.length > 0
    );
  }

  // Get transcription by ID
  async getTranscriptionById(id: number, companyId: number) {
    try {
      const transcription = await db.query.transcriptions.findFirst({
        where: and(eq(transcriptions.id, id), eq(transcriptions.companyId, companyId)),
        with: {
          customer: true,
          serviceTicket: true,
        }
      });

      if (!transcription) {
        throw new Error('Transcription not found');
      }

      return transcription;
    } catch (error) {
      console.error('Error fetching transcription:', error);
      throw new Error('Failed to fetch transcription');
    }
  }

  // Get transcriptions for customer
  async getCustomerTranscriptions(customerId: number, companyId: number) {
    try {
      return await db.query.transcriptions.findMany({
        where: and(
          eq(transcriptions.customerId, customerId),
          eq(transcriptions.companyId, companyId)
        ),
        orderBy: desc(transcriptions.createdAt),
        limit: 10,
      });
    } catch (error) {
      console.error('Error fetching customer transcriptions:', error);
      throw new Error('Failed to fetch customer transcriptions');
    }
  }

  // Process voicemail
  async processVoicemail(audioUrl: string, companyId: number, customerPhone?: string) {
    try {
      // Find customer by phone if provided
      let customerId;
      if (customerPhone) {
        const customer = await db.query.customers.findFirst({
          where: and(
            eq(customers.companyId, companyId),
            sql`(${customers.phone} = ${customerPhone} OR ${customers.alternatePhone} = ${customerPhone})`
          ),
        });
        customerId = customer?.id;
      }

      // Transcribe voicemail
      const result = await this.transcribeAudio({
        companyId,
        customerId,
        audioUrl,
        context: 'voicemail',
      });

      // Auto-create service ticket if needed
      if (result.shouldCreateTicket && customerId) {
        await serviceTicketService.createServiceTicket({
          companyId,
          customerId,
          title: `Zgłoszenie z poczty głosowej - ${result.analysis.intent}`,
          description: result.transcript,
          priority: result.priority,
          serviceType: 'repair',
          problemCategory: result.analysis.equipmentMentioned[0] || 'general',
        });
      }

      return result;
    } catch (error) {
      console.error('Error processing voicemail:', error);
      throw new Error('Failed to process voicemail');
    }
  }

  // Generate summary for technician
  async generateTechnicianSummary(transcriptionId: number, companyId: number) {
    try {
      const transcription = await this.getTranscriptionById(transcriptionId, companyId);
      
      if (!openai) {
        return this.generateBasicSummary(transcription);
      }

      const prompt = `
Create a concise technical summary for an HVAC technician based on this call transcript:

Transcript: ${transcription.transcript}
Context: ${transcription.context}

Provide a summary in Polish that includes:
1. Problem description
2. Equipment involved
3. Urgency level
4. Recommended actions
5. Customer contact info

Keep it professional and technical, max 200 words.
`;

      const completion = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: "You are creating technical summaries for HVAC technicians. Be concise and practical."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 300,
      });

      return completion.choices[0]?.message?.content || this.generateBasicSummary(transcription);
    } catch (error) {
      console.error('Error generating technician summary:', error);
      return this.generateBasicSummary(transcription);
    }
  }

  // Basic summary generation
  private generateBasicSummary(transcription: any): string {
    const analysis = JSON.parse(transcription.analysis || '{}');
    
    return `
**Podsumowanie zgłoszenia**

**Problem:** ${analysis.problemDescription || transcription.transcript.substring(0, 100)}
**Sprzęt:** ${analysis.equipmentMentioned?.join(', ') || 'Nieokreślony'}
**Priorytet:** ${transcription.urgencyLevel}
**Klient:** ${transcription.customer?.firstName} ${transcription.customer?.lastName}
**Telefon:** ${transcription.customer?.phone}

**Zalecane działania:**
${analysis.actionItems?.join('\n') || '- Kontakt z klientem w celu ustalenia szczegółów'}
`;
  }
}

export const transcriptionService = new TranscriptionService();