// 🌟 HVAC Customers API - PEŁNA MOC WIATRU! ⚡
// Complete customer management API with AI insights

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { customerService } from '@/services/customer.service';

// Validation schemas
const createCustomerSchema = z.object({
  companyId: z.number().positive(),
  firstName: z.string().min(1).max(100),
  lastName: z.string().min(1).max(100),
  email: z.string().email().optional(),
  phone: z.string().max(50).optional(),
  alternatePhone: z.string().max(50).optional(),
  address: z.string().optional(),
  city: z.string().max(100).optional(),
  state: z.string().max(50).optional(),
  zipCode: z.string().max(20).optional(),
  country: z.string().max(100).optional(),
  preferredContact: z.enum(['email', 'phone', 'sms', 'in_person', 'chat']).optional(),
  source: z.string().max(100).optional(),
  notes: z.string().optional(),
  tags: z.array(z.string()).optional(),
});

const updateCustomerSchema = createCustomerSchema.partial().extend({
  flowState: z.enum([
    'initial_contact',
    'quote_requested', 
    'quote_sent',
    'quote_approved',
    'service_scheduled',
    'service_in_progress',
    'service_completed',
    'invoice_sent',
    'payment_received',
    'follow_up_scheduled',
    'maintenance_program'
  ]).optional(),
  customerValue: z.number().optional(),
  lastServiceDate: z.string().datetime().optional(),
  nextMaintenanceDate: z.string().datetime().optional(),
  healthScore: z.number().min(0).max(100).optional(),
  churnRisk: z.number().min(0).max(100).optional(),
  sentimentScore: z.number().min(-1).max(1).optional(),
});

const filtersSchema = z.object({
  companyId: z.number().positive(),
  search: z.string().optional(),
  flowState: z.enum([
    'initial_contact',
    'quote_requested', 
    'quote_sent',
    'quote_approved',
    'service_scheduled',
    'service_in_progress',
    'service_completed',
    'invoice_sent',
    'payment_received',
    'follow_up_scheduled',
    'maintenance_program'
  ]).optional(),
  city: z.string().optional(),
  isActive: z.boolean().optional(),
  healthScoreMin: z.number().min(0).max(100).optional(),
  healthScoreMax: z.number().min(0).max(100).optional(),
  churnRiskMin: z.number().min(0).max(100).optional(),
  churnRiskMax: z.number().min(0).max(100).optional(),
  page: z.number().positive().optional(),
  limit: z.number().positive().max(100).optional(),
});

// GET /api/customers - Get customers with filters
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Parse query parameters
    const queryParams = {
      companyId: Number(searchParams.get('companyId')),
      search: searchParams.get('search') || undefined,
      flowState: searchParams.get('flowState') || undefined,
      city: searchParams.get('city') || undefined,
      isActive: searchParams.get('isActive') ? searchParams.get('isActive') === 'true' : undefined,
      healthScoreMin: searchParams.get('healthScoreMin') ? Number(searchParams.get('healthScoreMin')) : undefined,
      healthScoreMax: searchParams.get('healthScoreMax') ? Number(searchParams.get('healthScoreMax')) : undefined,
      churnRiskMin: searchParams.get('churnRiskMin') ? Number(searchParams.get('churnRiskMin')) : undefined,
      churnRiskMax: searchParams.get('churnRiskMax') ? Number(searchParams.get('churnRiskMax')) : undefined,
      page: searchParams.get('page') ? Number(searchParams.get('page')) : 1,
      limit: searchParams.get('limit') ? Number(searchParams.get('limit')) : 20,
    };

    // Validate parameters
    const validatedParams = filtersSchema.parse(queryParams);
    
    const { page, limit, ...filters } = validatedParams;
    
    // Get customers
    const result = await customerService.getCustomers(filters, page, limit);
    
    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error('GET /api/customers error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid parameters', details: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { success: false, error: 'Failed to fetch customers' },
      { status: 500 }
    );
  }
}

// POST /api/customers - Create new customer
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate request body
    const validatedData = createCustomerSchema.parse(body);
    
    // Create customer
    const customer = await customerService.createCustomer(validatedData);
    
    return NextResponse.json({
      success: true,
      data: customer,
    }, { status: 201 });
  } catch (error) {
    console.error('POST /api/customers error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { success: false, error: 'Failed to create customer' },
      { status: 500 }
    );
  }
}