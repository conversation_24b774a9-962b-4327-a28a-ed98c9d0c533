// 📊 Customer Analytics API - PEŁNA MOC WIATRU! ⚡

import { NextRequest, NextResponse } from 'next/server';
import { customerService } from '@/services/customer.service';

// GET /api/customers/analytics - Get customer analytics
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const companyId = Number(searchParams.get('companyId'));

    if (!companyId) {
      return NextResponse.json(
        { success: false, error: 'Company ID is required' },
        { status: 400 }
      );
    }

    const analytics = await customerService.getCustomerAnalytics(companyId);
    
    return NextResponse.json({
      success: true,
      data: analytics,
    });
  } catch (error) {
    console.error('GET /api/customers/analytics error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to fetch customer analytics' },
      { status: 500 }
    );
  }
}