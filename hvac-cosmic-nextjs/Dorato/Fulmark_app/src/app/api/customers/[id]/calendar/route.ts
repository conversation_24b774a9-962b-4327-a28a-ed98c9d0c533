import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '../../../../../libs/db';
import { calendarConnections, calendarEvents, customerCalendarInsights } from '../../../../../models/calendar';
import { eq, and, desc, gte, lte } from 'drizzle-orm';

interface RouteParams {
  params: {
    id: string;
  };
}

/**
 * Get calendar events and insights for a specific customer
 */
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    // Check authentication
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const customerId = params.id;
    const searchParams = request.nextUrl.searchParams;
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const limit = parseInt(searchParams.get('limit') || '50');
    const includeInsights = searchParams.get('includeInsights') === 'true';

    // Verify user has access to this customer's calendar events
    const userConnections = await db
      .select({ id: calendarConnections.id })
      .from(calendarConnections)
      .where(eq(calendarConnections.userId, userId));

    if (!userConnections.length) {
      return NextResponse.json({
        events: [],
        insights: null,
        message: 'No calendar connections found',
      });
    }

    const connectionIds = userConnections.map(conn => conn.id);

    // Build query conditions
    const conditions = [
      eq(calendarEvents.customerId, customerId),
      calendarEvents.connectionId.in ? calendarEvents.connectionId.in(connectionIds) : undefined
    ].filter(Boolean);

    // Add date range filter
    if (startDate) {
      conditions.push(gte(calendarEvents.startTime, new Date(startDate)));
    }
    if (endDate) {
      conditions.push(lte(calendarEvents.endTime, new Date(endDate)));
    }

    // Get calendar events for this customer
    const events = await db
      .select({
        id: calendarEvents.id,
        microsoftEventId: calendarEvents.microsoftEventId,
        subject: calendarEvents.subject,
        bodyContent: calendarEvents.bodyContent,
        startTime: calendarEvents.startTime,
        endTime: calendarEvents.endTime,
        location: calendarEvents.location,
        attendees: calendarEvents.attendees,
        organizer: calendarEvents.organizer,
        isAllDay: calendarEvents.isAllDay,
        isCancelled: calendarEvents.isCancelled,
        importance: calendarEvents.importance,
        eventType: calendarEvents.eventType,
        priority: calendarEvents.priority,
        confidenceScore: calendarEvents.confidenceScore,
        sentimentScore: calendarEvents.sentimentScore,
        aiAnalysis: calendarEvents.aiAnalysis,
        extractedCustomerInfo: calendarEvents.extractedCustomerInfo,
        actionItems: calendarEvents.actionItems,
        createdAt: calendarEvents.createdAt,
        updatedAt: calendarEvents.updatedAt,
      })
      .from(calendarEvents)
      .where(and(...conditions))
      .orderBy(desc(calendarEvents.startTime))
      .limit(limit);

    // Get customer calendar insights if requested
    let insights = null;
    if (includeInsights) {
      const insightsResult = await db
        .select()
        .from(customerCalendarInsights)
        .where(eq(customerCalendarInsights.customerId, customerId))
        .limit(1);

      insights = insightsResult.length > 0 ? insightsResult[0] : null;
    }

    // Calculate basic statistics
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const futureEvents = events.filter(event => 
      event.startTime && new Date(event.startTime) > now
    );
    const recentEvents = events.filter(event => 
      event.startTime && new Date(event.startTime) >= thirtyDaysAgo && new Date(event.startTime) <= now
    );

    // Group events by type
    const eventsByType = events.reduce((acc, event) => {
      const type = event.eventType || 'other';
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Calculate average sentiment
    const sentimentScores = events
      .map(event => parseFloat(event.sentimentScore || '0'))
      .filter(score => !isNaN(score));
    const averageSentiment = sentimentScores.length > 0
      ? sentimentScores.reduce((sum, score) => sum + score, 0) / sentimentScores.length
      : 0;

    return NextResponse.json({
      events,
      insights,
      statistics: {
        totalEvents: events.length,
        futureEvents: futureEvents.length,
        recentEvents: recentEvents.length,
        eventsByType,
        averageSentiment: Math.round(averageSentiment * 100) / 100,
        lastEventDate: events.length > 0 ? events[0].startTime : null,
        nextEventDate: futureEvents.length > 0 ? futureEvents[futureEvents.length - 1].startTime : null,
      },
    });

  } catch (error) {
    console.error('Customer calendar API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch customer calendar data' },
      { status: 500 }
    );
  }
}

/**
 * Update customer calendar insights
 */
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    // Check authentication
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const customerId = params.id;

    // Verify user has access to this customer
    const userConnections = await db
      .select({ id: calendarConnections.id })
      .from(calendarConnections)
      .where(eq(calendarConnections.userId, userId));

    if (!userConnections.length) {
      return NextResponse.json(
        { error: 'No calendar connections found' },
        { status: 404 }
      );
    }

    const connectionIds = userConnections.map(conn => conn.id);

    // Get all calendar events for this customer
    const events = await db
      .select()
      .from(calendarEvents)
      .where(
        and(
          eq(calendarEvents.customerId, customerId),
          calendarEvents.connectionId.in ? calendarEvents.connectionId.in(connectionIds) : undefined
        )
      )
      .orderBy(desc(calendarEvents.startTime));

    if (!events.length) {
      return NextResponse.json({
        message: 'No calendar events found for this customer',
        insights: null,
      });
    }

    // Calculate insights
    const now = new Date();
    const pastEvents = events.filter(event => 
      event.startTime && new Date(event.startTime) <= now
    );
    const futureEvents = events.filter(event => 
      event.startTime && new Date(event.startTime) > now
    );

    // Calculate meeting frequency
    let meetingFrequencyDays = 0;
    if (pastEvents.length > 1) {
      const sortedPastEvents = pastEvents.sort((a, b) => 
        new Date(a.startTime!).getTime() - new Date(b.startTime!).getTime()
      );
      const firstEvent = new Date(sortedPastEvents[0].startTime!);
      const lastEvent = new Date(sortedPastEvents[sortedPastEvents.length - 1].startTime!);
      const daysDiff = (lastEvent.getTime() - firstEvent.getTime()) / (1000 * 60 * 60 * 24);
      meetingFrequencyDays = daysDiff / (pastEvents.length - 1);
    }

    // Analyze preferred meeting times
    const hourCounts: Record<number, number> = {};
    events.forEach(event => {
      if (event.startTime) {
        const hour = new Date(event.startTime).getHours();
        hourCounts[hour] = (hourCounts[hour] || 0) + 1;
      }
    });

    // Find most common meeting types
    const typeCounts: Record<string, number> = {};
    events.forEach(event => {
      const type = event.eventType || 'other';
      typeCounts[type] = (typeCounts[type] || 0) + 1;
    });
    const commonMeetingTypes = Object.entries(typeCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([type]) => type);

    // Calculate engagement score based on various factors
    const avgConfidence = events.reduce((sum, event) => 
      sum + parseFloat(event.confidenceScore || '0'), 0) / events.length;
    const avgSentiment = events.reduce((sum, event) => 
      sum + parseFloat(event.sentimentScore || '0'), 0) / events.length;
    const recentActivity = pastEvents.filter(event => 
      event.startTime && new Date(event.startTime) > new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
    ).length;
    
    const engagementScore = Math.min(1.0, 
      (avgConfidence * 0.3) + 
      ((avgSentiment + 1) / 2 * 0.3) + 
      (Math.min(recentActivity / 10, 1) * 0.4)
    );

    // Prepare insights data
    const insightsData = {
      customerId,
      totalMeetings: events.length,
      lastMeetingDate: pastEvents.length > 0 ? new Date(pastEvents[0].startTime!) : null,
      nextMeetingDate: futureEvents.length > 0 ? new Date(futureEvents[0].startTime!) : null,
      meetingFrequencyDays: meetingFrequencyDays.toString(),
      preferredMeetingTimes: hourCounts,
      commonMeetingTypes,
      engagementScore: engagementScore.toString(),
      lastCalculatedAt: new Date(),
    };

    // Upsert insights
    const existingInsights = await db
      .select()
      .from(customerCalendarInsights)
      .where(eq(customerCalendarInsights.customerId, customerId))
      .limit(1);

    let result;
    if (existingInsights.length > 0) {
      result = await db
        .update(customerCalendarInsights)
        .set(insightsData)
        .where(eq(customerCalendarInsights.customerId, customerId))
        .returning();
    } else {
      result = await db
        .insert(customerCalendarInsights)
        .values(insightsData)
        .returning();
    }

    return NextResponse.json({
      message: 'Customer calendar insights updated successfully',
      insights: result[0],
    });

  } catch (error) {
    console.error('Customer calendar insights API error:', error);
    return NextResponse.json(
      { error: 'Failed to update customer calendar insights' },
      { status: 500 }
    );
  }
}
