// 🌟 Individual Customer API - PEŁNA MOC WIATRU! ⚡

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { customerService } from '@/services/customer.service';

const updateCustomerSchema = z.object({
  companyId: z.number().positive(),
  firstName: z.string().min(1).max(100).optional(),
  lastName: z.string().min(1).max(100).optional(),
  email: z.string().email().optional(),
  phone: z.string().max(50).optional(),
  alternatePhone: z.string().max(50).optional(),
  address: z.string().optional(),
  city: z.string().max(100).optional(),
  state: z.string().max(50).optional(),
  zipCode: z.string().max(20).optional(),
  country: z.string().max(100).optional(),
  preferredContact: z.enum(['email', 'phone', 'sms', 'in_person', 'chat']).optional(),
  flowState: z.enum([
    'initial_contact', 'quote_requested', 'quote_sent', 'quote_approved',
    'service_scheduled', 'service_in_progress', 'service_completed',
    'invoice_sent', 'payment_received', 'follow_up_scheduled', 'maintenance_program'
  ]).optional(),
  source: z.string().max(100).optional(),
  notes: z.string().optional(),
  tags: z.array(z.string()).optional(),
  customerValue: z.number().optional(),
  lastServiceDate: z.string().datetime().optional(),
  nextMaintenanceDate: z.string().datetime().optional(),
  healthScore: z.number().min(0).max(100).optional(),
  churnRisk: z.number().min(0).max(100).optional(),
  sentimentScore: z.number().min(-1).max(1).optional(),
});

// GET /api/customers/[id] - Get customer by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const customerId = Number(params.id);
    const { searchParams } = new URL(request.url);
    const companyId = Number(searchParams.get('companyId'));

    if (!customerId || !companyId) {
      return NextResponse.json(
        { success: false, error: 'Invalid customer ID or company ID' },
        { status: 400 }
      );
    }

    const customer = await customerService.getCustomerById(customerId, companyId);
    
    return NextResponse.json({
      success: true,
      data: customer,
    });
  } catch (error) {
    console.error('GET /api/customers/[id] error:', error);
    
    if (error instanceof Error && error.message === 'Customer not found') {
      return NextResponse.json(
        { success: false, error: 'Customer not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(
      { success: false, error: 'Failed to fetch customer' },
      { status: 500 }
    );
  }
}

// PUT /api/customers/[id] - Update customer
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const customerId = Number(params.id);
    const body = await request.json();

    if (!customerId) {
      return NextResponse.json(
        { success: false, error: 'Invalid customer ID' },
        { status: 400 }
      );
    }

    // Validate request body
    const validatedData = updateCustomerSchema.parse(body);
    const { companyId, ...updateData } = validatedData;

    // Convert date strings to Date objects
    const processedData = {
      ...updateData,
      lastServiceDate: updateData.lastServiceDate ? new Date(updateData.lastServiceDate) : undefined,
      nextMaintenanceDate: updateData.nextMaintenanceDate ? new Date(updateData.nextMaintenanceDate) : undefined,
    };

    // Update customer
    const customer = await customerService.updateCustomer(customerId, companyId, processedData);
    
    return NextResponse.json({
      success: true,
      data: customer,
    });
  } catch (error) {
    console.error('PUT /api/customers/[id] error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }
    
    if (error instanceof Error && error.message === 'Customer not found') {
      return NextResponse.json(
        { success: false, error: 'Customer not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(
      { success: false, error: 'Failed to update customer' },
      { status: 500 }
    );
  }
}

// DELETE /api/customers/[id] - Delete customer (soft delete)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const customerId = Number(params.id);
    const { searchParams } = new URL(request.url);
    const companyId = Number(searchParams.get('companyId'));

    if (!customerId || !companyId) {
      return NextResponse.json(
        { success: false, error: 'Invalid customer ID or company ID' },
        { status: 400 }
      );
    }

    const customer = await customerService.deleteCustomer(customerId, companyId);
    
    return NextResponse.json({
      success: true,
      data: customer,
    });
  } catch (error) {
    console.error('DELETE /api/customers/[id] error:', error);
    
    if (error instanceof Error && error.message === 'Customer not found') {
      return NextResponse.json(
        { success: false, error: 'Customer not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(
      { success: false, error: 'Failed to delete customer' },
      { status: 500 }
    );
  }
}