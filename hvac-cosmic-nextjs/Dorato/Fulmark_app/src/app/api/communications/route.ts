// 📞 Communications API - PEŁNA MOC WIATRU! ⚡

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { communicationService } from '@/services/communication.service';

const createCommunicationSchema = z.object({
  companyId: z.number().positive(),
  customerId: z.number().positive().optional(),
  serviceTicketId: z.number().positive().optional(),
  type: z.enum(['email', 'phone', 'sms', 'in_person', 'chat']),
  direction: z.enum(['inbound', 'outbound']),
  subject: z.string().max(500).optional(),
  content: z.string().min(1),
  fromEmail: z.string().email().optional(),
  toEmail: z.string().email().optional(),
  fromPhone: z.string().max(50).optional(),
  toPhone: z.string().max(50).optional(),
  attachments: z.array(z.string()).optional(),
  threadId: z.string().max(100).optional(),
  parentId: z.number().positive().optional(),
});

const filtersSchema = z.object({
  companyId: z.number().positive(),
  customerId: z.number().positive().optional(),
  serviceTicketId: z.number().positive().optional(),
  type: z.array(z.enum(['email', 'phone', 'sms', 'in_person', 'chat'])).optional(),
  direction: z.enum(['inbound', 'outbound']).optional(),
  category: z.string().optional(),
  priority: z.array(z.enum(['low', 'normal', 'high', 'urgent', 'emergency'])).optional(),
  isProcessed: z.boolean().optional(),
  requiresHumanAttention: z.boolean().optional(),
  dateFrom: z.string().datetime().optional(),
  dateTo: z.string().datetime().optional(),
  search: z.string().optional(),
  page: z.number().positive().optional(),
  limit: z.number().positive().max(100).optional(),
});

// GET /api/communications - Get communications with filters
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    const queryParams = {
      companyId: Number(searchParams.get('companyId')),
      customerId: searchParams.get('customerId') ? Number(searchParams.get('customerId')) : undefined,
      serviceTicketId: searchParams.get('serviceTicketId') ? Number(searchParams.get('serviceTicketId')) : undefined,
      type: searchParams.get('type')?.split(',') as any,
      direction: searchParams.get('direction') as any,
      category: searchParams.get('category') || undefined,
      priority: searchParams.get('priority')?.split(',') as any,
      isProcessed: searchParams.get('isProcessed') ? searchParams.get('isProcessed') === 'true' : undefined,
      requiresHumanAttention: searchParams.get('requiresHumanAttention') ? searchParams.get('requiresHumanAttention') === 'true' : undefined,
      dateFrom: searchParams.get('dateFrom') || undefined,
      dateTo: searchParams.get('dateTo') || undefined,
      search: searchParams.get('search') || undefined,
      page: searchParams.get('page') ? Number(searchParams.get('page')) : 1,
      limit: searchParams.get('limit') ? Number(searchParams.get('limit')) : 20,
    };

    const validatedParams = filtersSchema.parse(queryParams);
    const { page, limit, ...filters } = validatedParams;
    
    // Convert date strings to Date objects
    const processedFilters = {
      ...filters,
      dateFrom: filters.dateFrom ? new Date(filters.dateFrom) : undefined,
      dateTo: filters.dateTo ? new Date(filters.dateTo) : undefined,
    };
    
    const result = await communicationService.getCommunications(processedFilters, page, limit);
    
    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error('GET /api/communications error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid parameters', details: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { success: false, error: 'Failed to fetch communications' },
      { status: 500 }
    );
  }
}

// POST /api/communications - Create new communication
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const validatedData = createCommunicationSchema.parse(body);
    
    const communication = await communicationService.createCommunication(validatedData);
    
    return NextResponse.json({
      success: true,
      data: communication,
    }, { status: 201 });
  } catch (error) {
    console.error('POST /api/communications error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { success: false, error: 'Failed to create communication' },
      { status: 500 }
    );
  }
}