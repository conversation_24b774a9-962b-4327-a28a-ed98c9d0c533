// 🔧 Service Tickets API - PEŁNA MOC WIATRU! ⚡

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { serviceTicketService } from '@/services/serviceTicket.service';

const createServiceTicketSchema = z.object({
  companyId: z.number().positive(),
  customerId: z.number().positive(),
  buildingId: z.number().positive().optional(),
  title: z.string().min(1).max(255),
  description: z.string().optional(),
  priority: z.enum(['low', 'normal', 'high', 'urgent', 'emergency']).optional(),
  serviceType: z.string().max(100).optional(),
  equipmentType: z.string().max(100).optional(),
  problemCategory: z.string().max(100).optional(),
  scheduledDate: z.string().datetime().optional(),
  estimatedDuration: z.number().positive().optional(),
  estimatedCost: z.number().positive().optional(),
});

const filtersSchema = z.object({
  companyId: z.number().positive(),
  status: z.array(z.enum(['new', 'assigned', 'in_progress', 'waiting_parts', 'completed', 'cancelled', 'on_hold'])).optional(),
  priority: z.array(z.enum(['low', 'normal', 'high', 'urgent', 'emergency'])).optional(),
  assignedTechnicianId: z.number().positive().optional(),
  customerId: z.number().positive().optional(),
  serviceType: z.string().optional(),
  dateFrom: z.string().datetime().optional(),
  dateTo: z.string().datetime().optional(),
  search: z.string().optional(),
  page: z.number().positive().optional(),
  limit: z.number().positive().max(100).optional(),
});

// GET /api/service-tickets - Get service tickets with filters
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    const queryParams = {
      companyId: Number(searchParams.get('companyId')),
      status: searchParams.get('status')?.split(',') as any,
      priority: searchParams.get('priority')?.split(',') as any,
      assignedTechnicianId: searchParams.get('assignedTechnicianId') ? Number(searchParams.get('assignedTechnicianId')) : undefined,
      customerId: searchParams.get('customerId') ? Number(searchParams.get('customerId')) : undefined,
      serviceType: searchParams.get('serviceType') || undefined,
      dateFrom: searchParams.get('dateFrom') || undefined,
      dateTo: searchParams.get('dateTo') || undefined,
      search: searchParams.get('search') || undefined,
      page: searchParams.get('page') ? Number(searchParams.get('page')) : 1,
      limit: searchParams.get('limit') ? Number(searchParams.get('limit')) : 20,
    };

    const validatedParams = filtersSchema.parse(queryParams);
    const { page, limit, ...filters } = validatedParams;
    
    // Convert date strings to Date objects
    const processedFilters = {
      ...filters,
      dateFrom: filters.dateFrom ? new Date(filters.dateFrom) : undefined,
      dateTo: filters.dateTo ? new Date(filters.dateTo) : undefined,
    };
    
    const result = await serviceTicketService.getServiceTickets(processedFilters, page, limit);
    
    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error('GET /api/service-tickets error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid parameters', details: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { success: false, error: 'Failed to fetch service tickets' },
      { status: 500 }
    );
  }
}

// POST /api/service-tickets - Create new service ticket
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const validatedData = createServiceTicketSchema.parse(body);
    
    // Convert date strings to Date objects
    const processedData = {
      ...validatedData,
      scheduledDate: validatedData.scheduledDate ? new Date(validatedData.scheduledDate) : undefined,
    };
    
    const ticket = await serviceTicketService.createServiceTicket(processedData);
    
    return NextResponse.json({
      success: true,
      data: ticket,
    }, { status: 201 });
  } catch (error) {
    console.error('POST /api/service-tickets error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { success: false, error: 'Failed to create service ticket' },
      { status: 500 }
    );
  }
}