import { NextRequest, NextResponse } from 'next/server';
import { microsoftGraphService } from '../../../../services/microsoft-graph';
import { tokenEncryptionService } from '../../../../services/token-encryption';
import { db } from '../../../../libs/db';
import { calendarConnections } from '../../../../models/calendar';
import { eq } from 'drizzle-orm';

/**
 * Handle Microsoft Graph OAuth callback
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const error = searchParams.get('error');
    const errorDescription = searchParams.get('error_description');

    // Handle OAuth errors
    if (error) {
      console.error('OAuth error:', error, errorDescription);
      return NextResponse.redirect(
        new URL(`/dashboard?calendar_error=${encodeURIComponent(errorDescription || error)}`, request.url)
      );
    }

    // Validate required parameters
    if (!code || !state) {
      return NextResponse.redirect(
        new URL('/dashboard?calendar_error=Invalid_callback_parameters', request.url)
      );
    }

    // Decode and validate state
    let stateData;
    try {
      const decodedState = Buffer.from(state, 'base64').toString('utf-8');
      stateData = JSON.parse(decodedState);
    } catch (error) {
      console.error('Invalid state parameter:', error);
      return NextResponse.redirect(
        new URL('/dashboard?calendar_error=Invalid_state_parameter', request.url)
      );
    }

    const { userId, timestamp } = stateData;

    // Check state timestamp (should be within 10 minutes)
    if (Date.now() - timestamp > 10 * 60 * 1000) {
      return NextResponse.redirect(
        new URL('/dashboard?calendar_error=State_expired', request.url)
      );
    }

    // Exchange code for tokens
    const tokenData = await microsoftGraphService.getTokenFromCode(code, state);

    // Get user profile to verify access
    const userProfile = await microsoftGraphService.getUserProfile(tokenData.accessToken);

    // Encrypt tokens for storage
    const encryptedAccessToken = await tokenEncryptionService.encrypt(tokenData.accessToken);
    const encryptedRefreshToken = tokenData.refreshToken 
      ? await tokenEncryptionService.encrypt(tokenData.refreshToken)
      : null;

    // Check if connection already exists for this user
    const existingConnection = await db
      .select()
      .from(calendarConnections)
      .where(eq(calendarConnections.userId, userId))
      .limit(1);

    if (existingConnection.length > 0) {
      // Update existing connection
      await db
        .update(calendarConnections)
        .set({
          accessTokenEncrypted: encryptedAccessToken,
          refreshTokenEncrypted: encryptedRefreshToken,
          expiresAt: tokenData.expiresOn || null,
          scopes: tokenData.scopes || [],
          isActive: true,
          syncStatus: 'pending',
          errorMessage: null,
          microsoftTenantId: tokenData.account?.tenantId || null,
          updatedAt: new Date(),
        })
        .where(eq(calendarConnections.id, existingConnection[0].id));

      console.log(`Updated calendar connection for user ${userId}`);
    } else {
      // Create new connection
      await db.insert(calendarConnections).values({
        userId,
        microsoftTenantId: tokenData.account?.tenantId || null,
        accessTokenEncrypted: encryptedAccessToken,
        refreshTokenEncrypted: encryptedRefreshToken,
        expiresAt: tokenData.expiresOn || null,
        scopes: tokenData.scopes || [],
        isActive: true,
        syncStatus: 'pending',
      });

      console.log(`Created new calendar connection for user ${userId}`);
    }

    // Get the connection ID for webhook setup
    const connections = await db
      .select()
      .from(calendarConnections)
      .where(eq(calendarConnections.userId, userId))
      .limit(1);

    if (connections.length > 0) {
      const connectionId = connections[0].id;

      // Set up webhook subscription for real-time updates
      try {
        const webhookUrl = `${process.env.NEXTAUTH_URL || 'https://your-domain.com'}/api/calendar/webhook`;
        const expirationDate = new Date();
        expirationDate.setDate(expirationDate.getDate() + 3); // 3 days from now

        const subscription = await microsoftGraphService.createCalendarSubscription(
          tokenData.accessToken,
          webhookUrl,
          expirationDate
        );

        // Update connection with subscription ID
        await db
          .update(calendarConnections)
          .set({
            subscriptionId: subscription.id,
            subscriptionExpiresAt: new Date(subscription.expirationDateTime),
            updatedAt: new Date(),
          })
          .where(eq(calendarConnections.id, connectionId));

        console.log(`Created webhook subscription ${subscription.id} for connection ${connectionId}`);
      } catch (webhookError) {
        console.error('Failed to create webhook subscription:', webhookError);
        // Don't fail the entire connection process if webhook setup fails
      }

      // Trigger initial sync (async, don't wait for completion)
      const { calendarSyncService } = await import('../../../../services/calendar-sync');
      calendarSyncService.syncCalendarEvents(connectionId, 'full').catch(error => {
        console.error('Initial calendar sync failed:', error);
      });
    }

    // Redirect to dashboard with success message
    return NextResponse.redirect(
      new URL('/dashboard?calendar_success=connected', request.url)
    );

  } catch (error) {
    console.error('Calendar callback error:', error);
    return NextResponse.redirect(
      new URL(`/dashboard?calendar_error=${encodeURIComponent('Connection_failed')}`, request.url)
    );
  }
}
