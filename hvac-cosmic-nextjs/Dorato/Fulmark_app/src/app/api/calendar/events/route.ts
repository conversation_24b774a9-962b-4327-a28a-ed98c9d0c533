import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '../../../../libs/db';
import { calendarConnections, calendarEvents } from '../../../../models/calendar';
import { eq, and, gte, lte, desc, asc } from 'drizzle-orm';

/**
 * Get calendar events for the authenticated user
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const searchParams = request.nextUrl.searchParams;
    const customerId = searchParams.get('customerId');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const eventType = searchParams.get('eventType');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    const sortBy = searchParams.get('sortBy') || 'startTime';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    // Get user's calendar connections
    const connections = await db
      .select({ id: calendarConnections.id })
      .from(calendarConnections)
      .where(eq(calendarConnections.userId, userId));

    if (!connections.length) {
      return NextResponse.json({
        events: [],
        total: 0,
        message: 'No calendar connections found',
      });
    }

    const connectionIds = connections.map(conn => conn.id);

    // Build query conditions
    const conditions = [
      calendarEvents.connectionId.in ? calendarEvents.connectionId.in(connectionIds) : undefined
    ].filter(Boolean);

    // Add customer filter
    if (customerId) {
      conditions.push(eq(calendarEvents.customerId, customerId));
    }

    // Add date range filter
    if (startDate) {
      conditions.push(gte(calendarEvents.startTime, new Date(startDate)));
    }
    if (endDate) {
      conditions.push(lte(calendarEvents.endTime, new Date(endDate)));
    }

    // Add event type filter
    if (eventType) {
      conditions.push(eq(calendarEvents.eventType, eventType));
    }

    // Build sort order
    const sortColumn = sortBy === 'startTime' ? calendarEvents.startTime :
                      sortBy === 'subject' ? calendarEvents.subject :
                      sortBy === 'priority' ? calendarEvents.priority :
                      sortBy === 'confidenceScore' ? calendarEvents.confidenceScore :
                      calendarEvents.startTime;

    const orderBy = sortOrder === 'asc' ? asc(sortColumn) : desc(sortColumn);

    // Execute query
    const events = await db
      .select({
        id: calendarEvents.id,
        microsoftEventId: calendarEvents.microsoftEventId,
        customerId: calendarEvents.customerId,
        subject: calendarEvents.subject,
        bodyContent: calendarEvents.bodyContent,
        startTime: calendarEvents.startTime,
        endTime: calendarEvents.endTime,
        location: calendarEvents.location,
        attendees: calendarEvents.attendees,
        organizer: calendarEvents.organizer,
        isAllDay: calendarEvents.isAllDay,
        isCancelled: calendarEvents.isCancelled,
        importance: calendarEvents.importance,
        eventType: calendarEvents.eventType,
        priority: calendarEvents.priority,
        confidenceScore: calendarEvents.confidenceScore,
        sentimentScore: calendarEvents.sentimentScore,
        aiAnalysis: calendarEvents.aiAnalysis,
        extractedCustomerInfo: calendarEvents.extractedCustomerInfo,
        actionItems: calendarEvents.actionItems,
        createdAt: calendarEvents.createdAt,
        updatedAt: calendarEvents.updatedAt,
      })
      .from(calendarEvents)
      .where(conditions.length > 0 ? and(...conditions) : undefined)
      .orderBy(orderBy)
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await db
      .select({ count: calendarEvents.id })
      .from(calendarEvents)
      .where(conditions.length > 0 ? and(...conditions) : undefined);

    const total = totalResult.length;

    // Get event type statistics
    const eventTypeStats = await db
      .select({
        eventType: calendarEvents.eventType,
        count: calendarEvents.id,
      })
      .from(calendarEvents)
      .where(conditions.length > 0 ? and(...conditions) : undefined)
      .groupBy(calendarEvents.eventType);

    return NextResponse.json({
      events,
      total,
      limit,
      offset,
      hasMore: offset + limit < total,
      statistics: {
        eventTypes: eventTypeStats,
        totalEvents: total,
      },
    });

  } catch (error) {
    console.error('Calendar events API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch calendar events' },
      { status: 500 }
    );
  }
}

/**
 * Update calendar event (e.g., customer assignment, event type)
 */
export async function PATCH(request: NextRequest) {
  try {
    // Check authentication
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { eventId, customerId, eventType, priority, notes } = body;

    if (!eventId) {
      return NextResponse.json(
        { error: 'Event ID is required' },
        { status: 400 }
      );
    }

    // Verify the event belongs to the user
    const event = await db
      .select({
        id: calendarEvents.id,
        connectionId: calendarEvents.connectionId,
      })
      .from(calendarEvents)
      .innerJoin(calendarConnections, eq(calendarEvents.connectionId, calendarConnections.id))
      .where(
        and(
          eq(calendarEvents.id, eventId),
          eq(calendarConnections.userId, userId)
        )
      )
      .limit(1);

    if (!event.length) {
      return NextResponse.json(
        { error: 'Calendar event not found' },
        { status: 404 }
      );
    }

    // Prepare update data
    const updateData: any = {
      updatedAt: new Date(),
    };

    if (customerId !== undefined) {
      updateData.customerId = customerId;
    }

    if (eventType !== undefined) {
      updateData.eventType = eventType;
    }

    if (priority !== undefined) {
      updateData.priority = Math.max(1, Math.min(5, priority));
    }

    // Update the event
    const updatedEvent = await db
      .update(calendarEvents)
      .set(updateData)
      .where(eq(calendarEvents.id, eventId))
      .returning();

    return NextResponse.json({
      message: 'Calendar event updated successfully',
      event: updatedEvent[0],
    });

  } catch (error) {
    console.error('Calendar event update API error:', error);
    return NextResponse.json(
      { error: 'Failed to update calendar event' },
      { status: 500 }
    );
  }
}
