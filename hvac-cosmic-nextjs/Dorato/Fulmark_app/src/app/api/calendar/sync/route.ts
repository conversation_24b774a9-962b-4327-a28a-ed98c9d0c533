import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '../../../../libs/db';
import { calendarConnections } from '../../../../models/calendar';
import { eq } from 'drizzle-orm';
import { calendarSyncService } from '../../../../services/calendar-sync';

/**
 * Trigger manual calendar sync
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { connectionId, syncType = 'incremental' } = body;

    // Validate sync type
    if (!['full', 'incremental'].includes(syncType)) {
      return NextResponse.json(
        { error: 'Invalid sync type. Must be "full" or "incremental"' },
        { status: 400 }
      );
    }

    // Get user's calendar connections
    const connections = await db
      .select()
      .from(calendarConnections)
      .where(eq(calendarConnections.userId, userId));

    if (!connections.length) {
      return NextResponse.json(
        { error: 'No calendar connections found' },
        { status: 404 }
      );
    }

    // If connectionId is provided, sync specific connection
    if (connectionId) {
      const connection = connections.find(conn => conn.id === connectionId);
      if (!connection) {
        return NextResponse.json(
          { error: 'Calendar connection not found' },
          { status: 404 }
        );
      }

      if (!connection.isActive) {
        return NextResponse.json(
          { error: 'Calendar connection is not active' },
          { status: 400 }
        );
      }

      // Start sync (async)
      calendarSyncService.syncCalendarEvents(connectionId, syncType).catch(error => {
        console.error(`Sync failed for connection ${connectionId}:`, error);
      });

      return NextResponse.json({
        message: 'Calendar sync started',
        connectionId,
        syncType,
      });
    }

    // Sync all active connections
    const activeConnections = connections.filter(conn => conn.isActive);
    
    if (!activeConnections.length) {
      return NextResponse.json(
        { error: 'No active calendar connections found' },
        { status: 404 }
      );
    }

    // Start sync for all connections (async)
    const syncPromises = activeConnections.map(connection =>
      calendarSyncService.syncCalendarEvents(connection.id, syncType).catch(error => {
        console.error(`Sync failed for connection ${connection.id}:`, error);
      })
    );

    // Don't wait for completion, return immediately
    Promise.all(syncPromises);

    return NextResponse.json({
      message: 'Calendar sync started for all active connections',
      connectionsCount: activeConnections.length,
      syncType,
    });

  } catch (error) {
    console.error('Calendar sync API error:', error);
    return NextResponse.json(
      { error: 'Failed to start calendar sync' },
      { status: 500 }
    );
  }
}

/**
 * Get calendar sync status
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const searchParams = request.nextUrl.searchParams;
    const connectionId = searchParams.get('connectionId');

    // Get user's calendar connections
    const connections = await db
      .select()
      .from(calendarConnections)
      .where(eq(calendarConnections.userId, userId));

    if (!connections.length) {
      return NextResponse.json(
        { error: 'No calendar connections found' },
        { status: 404 }
      );
    }

    // If connectionId is provided, get status for specific connection
    if (connectionId) {
      const connection = connections.find(conn => conn.id === connectionId);
      if (!connection) {
        return NextResponse.json(
          { error: 'Calendar connection not found' },
          { status: 404 }
        );
      }

      const status = await calendarSyncService.getSyncStatus(connectionId);
      return NextResponse.json(status);
    }

    // Get status for all connections
    const statusPromises = connections.map(async (connection) => {
      try {
        const status = await calendarSyncService.getSyncStatus(connection.id);
        return {
          connectionId: connection.id,
          ...status,
        };
      } catch (error) {
        return {
          connectionId: connection.id,
          error: error instanceof Error ? error.message : 'Unknown error',
        };
      }
    });

    const statuses = await Promise.all(statusPromises);

    return NextResponse.json({
      connections: statuses,
      summary: {
        total: connections.length,
        active: connections.filter(conn => conn.isActive).length,
        syncing: connections.filter(conn => conn.syncStatus === 'syncing').length,
        errors: connections.filter(conn => conn.syncStatus === 'error').length,
      },
    });

  } catch (error) {
    console.error('Calendar sync status API error:', error);
    return NextResponse.json(
      { error: 'Failed to get calendar sync status' },
      { status: 500 }
    );
  }
}
