import { getTranslations } from 'next-intl/server';
import TechnicianDashboard from '@/components/hvac/technicians/TechnicianDashboard';

export async function generateMetadata(props: { params: { locale: string } }) {
  const t = await getTranslations({
    locale: props.params.locale,
    namespace: 'Technicians',
  });

  return {
    title: t('meta_title'),
    description: t('meta_description'),
  };
}

const TechniciansPage = () => {
  return <TechnicianDashboard />;
};

export default TechniciansPage;