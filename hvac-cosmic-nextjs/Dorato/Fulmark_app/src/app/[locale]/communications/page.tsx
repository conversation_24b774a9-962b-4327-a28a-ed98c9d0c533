import { getTranslations } from 'next-intl/server';
import CommunicationHub from '@/components/hvac/communications/CommunicationHub';

export async function generateMetadata(props: { params: { locale: string } }) {
  const t = await getTranslations({
    locale: props.params.locale,
    namespace: 'Communications',
  });

  return {
    title: t('meta_title'),
    description: t('meta_description'),
  };
}

const CommunicationsPage = () => {
  return <CommunicationHub />;
};

export default CommunicationsPage;