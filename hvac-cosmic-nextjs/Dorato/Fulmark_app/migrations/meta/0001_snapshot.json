{"id": "7dfcd297-b251-4ffd-a9a8-adac5980cf11", "prevId": "0896e842-e142-406c-99b2-a602f7fa8731", "version": "7", "dialect": "postgresql", "tables": {"public.buildings": {"name": "buildings", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "customer_id": {"name": "customer_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": true}, "city": {"name": "city", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "zip_code": {"name": "zip_code", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "building_type": {"name": "building_type", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "square_footage": {"name": "square_footage", "type": "integer", "primaryKey": false, "notNull": false}, "floors": {"name": "floors", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "year_built": {"name": "year_built", "type": "integer", "primaryKey": false, "notNull": false}, "heating_type": {"name": "heating_type", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "cooling_type": {"name": "cooling_type", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "latitude": {"name": "latitude", "type": "numeric(10, 8)", "primaryKey": false, "notNull": false}, "longitude": {"name": "longitude", "type": "numeric(11, 8)", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"buildings_customer_id_customers_id_fk": {"name": "buildings_customer_id_customers_id_fk", "tableFrom": "buildings", "tableTo": "customers", "columnsFrom": ["customer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.communications": {"name": "communications", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "integer", "primaryKey": false, "notNull": true}, "customer_id": {"name": "customer_id", "type": "integer", "primaryKey": false, "notNull": false}, "service_ticket_id": {"name": "service_ticket_id", "type": "integer", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "communication_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "direction": {"name": "direction", "type": "communication_direction", "typeSchema": "public", "primaryKey": false, "notNull": true}, "subject": {"name": "subject", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false}, "from_email": {"name": "from_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "to_email": {"name": "to_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "from_phone": {"name": "from_phone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "to_phone": {"name": "to_phone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "sentiment_score": {"name": "sentiment_score", "type": "numeric(3, 2)", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "priority": {"name": "priority", "type": "service_ticket_priority", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'normal'"}, "intent": {"name": "intent", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "auto_response_sent": {"name": "auto_response_sent", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "requires_human_attention": {"name": "requires_human_attention", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_processed": {"name": "is_processed", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "attachments": {"name": "attachments", "type": "json", "primaryKey": false, "notNull": false}, "transcription_id": {"name": "transcription_id", "type": "integer", "primaryKey": false, "notNull": false}, "thread_id": {"name": "thread_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"communications_company_id_companies_id_fk": {"name": "communications_company_id_companies_id_fk", "tableFrom": "communications", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "communications_customer_id_customers_id_fk": {"name": "communications_customer_id_customers_id_fk", "tableFrom": "communications", "tableTo": "customers", "columnsFrom": ["customer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "communications_service_ticket_id_service_tickets_id_fk": {"name": "communications_service_ticket_id_service_tickets_id_fk", "tableFrom": "communications", "tableTo": "service_tickets", "columnsFrom": ["service_ticket_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "communications_parent_id_communications_id_fk": {"name": "communications_parent_id_communications_id_fk", "tableFrom": "communications", "tableTo": "communications", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.companies": {"name": "companies", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "zip_code": {"name": "zip_code", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "default": "'Poland'"}, "website": {"name": "website", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "logo": {"name": "logo", "type": "text", "primaryKey": false, "notNull": false}, "settings": {"name": "settings", "type": "json", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.counter": {"name": "counter", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "count": {"name": "count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.customers": {"name": "customers", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "integer", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "alternate_phone": {"name": "alternate_phone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "zip_code": {"name": "zip_code", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "default": "'Poland'"}, "flow_state": {"name": "flow_state", "type": "customer_flow_state", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'initial_contact'"}, "preferred_contact": {"name": "preferred_contact", "type": "communication_type", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'email'"}, "customer_value": {"name": "customer_value", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "last_service_date": {"name": "last_service_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "next_maintenance_date": {"name": "next_maintenance_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "health_score": {"name": "health_score", "type": "integer", "primaryKey": false, "notNull": false, "default": 100}, "churn_risk": {"name": "churn_risk", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "sentiment_score": {"name": "sentiment_score", "type": "numeric(3, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "source": {"name": "source", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "tags": {"name": "tags", "type": "json", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"customers_company_id_companies_id_fk": {"name": "customers_company_id_companies_id_fk", "tableFrom": "customers", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.equipment": {"name": "equipment", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "building_id": {"name": "building_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "manufacturer": {"name": "manufacturer", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "model": {"name": "model", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "serial_number": {"name": "serial_number", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "equipment_type": {"name": "equipment_type", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "installation_date": {"name": "installation_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "warranty_expiration": {"name": "warranty_expiration", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_maintenance_date": {"name": "last_maintenance_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "next_maintenance_date": {"name": "next_maintenance_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "equipment_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'active'"}, "health_score": {"name": "health_score", "type": "integer", "primaryKey": false, "notNull": false, "default": 100}, "specifications": {"name": "specifications", "type": "json", "primaryKey": false, "notNull": false}, "manual_url": {"name": "manual_url", "type": "text", "primaryKey": false, "notNull": false}, "maintenance_history": {"name": "maintenance_history", "type": "json", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"equipment_building_id_buildings_id_fk": {"name": "equipment_building_id_buildings_id_fk", "tableFrom": "equipment", "tableTo": "buildings", "columnsFrom": ["building_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.invoices": {"name": "invoices", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "integer", "primaryKey": false, "notNull": true}, "customer_id": {"name": "customer_id", "type": "integer", "primaryKey": false, "notNull": true}, "service_ticket_id": {"name": "service_ticket_id", "type": "integer", "primaryKey": false, "notNull": false}, "invoice_number": {"name": "invoice_number", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "invoice_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'draft'"}, "subtotal": {"name": "subtotal", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "tax_amount": {"name": "tax_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "discount_amount": {"name": "discount_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "total_amount": {"name": "total_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "line_items": {"name": "line_items", "type": "json", "primaryKey": false, "notNull": false}, "issue_date": {"name": "issue_date", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "due_date": {"name": "due_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "paid_date": {"name": "paid_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "payment_method": {"name": "payment_method", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "payment_reference": {"name": "payment_reference", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "terms": {"name": "terms", "type": "text", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"invoices_company_id_companies_id_fk": {"name": "invoices_company_id_companies_id_fk", "tableFrom": "invoices", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "invoices_customer_id_customers_id_fk": {"name": "invoices_customer_id_customers_id_fk", "tableFrom": "invoices", "tableTo": "customers", "columnsFrom": ["customer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "invoices_service_ticket_id_service_tickets_id_fk": {"name": "invoices_service_ticket_id_service_tickets_id_fk", "tableFrom": "invoices", "tableTo": "service_tickets", "columnsFrom": ["service_ticket_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.parts_inventory": {"name": "parts_inventory", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "integer", "primaryKey": false, "notNull": true}, "part_number": {"name": "part_number", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "manufacturer": {"name": "manufacturer", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "quantity_in_stock": {"name": "quantity_in_stock", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "minimum_stock": {"name": "minimum_stock", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "maximum_stock": {"name": "maximum_stock", "type": "integer", "primaryKey": false, "notNull": false}, "reorder_point": {"name": "reorder_point", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "cost": {"name": "cost", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "price": {"name": "price", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "supplier_name": {"name": "supplier_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "supplier_part_number": {"name": "supplier_part_number", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "location": {"name": "location", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "weight": {"name": "weight", "type": "numeric(8, 3)", "primaryKey": false, "notNull": false}, "dimensions": {"name": "dimensions", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"parts_inventory_company_id_companies_id_fk": {"name": "parts_inventory_company_id_companies_id_fk", "tableFrom": "parts_inventory", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.service_tickets": {"name": "service_tickets", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "integer", "primaryKey": false, "notNull": true}, "customer_id": {"name": "customer_id", "type": "integer", "primaryKey": false, "notNull": true}, "building_id": {"name": "building_id", "type": "integer", "primaryKey": false, "notNull": false}, "assigned_technician_id": {"name": "assigned_technician_id", "type": "integer", "primaryKey": false, "notNull": false}, "ticket_number": {"name": "ticket_number", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "service_ticket_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'new'"}, "priority": {"name": "priority", "type": "service_ticket_priority", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'normal'"}, "service_type": {"name": "service_type", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "equipment_type": {"name": "equipment_type", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "problem_category": {"name": "problem_category", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "scheduled_date": {"name": "scheduled_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "estimated_duration": {"name": "estimated_duration", "type": "integer", "primaryKey": false, "notNull": false}, "actual_start_time": {"name": "actual_start_time", "type": "timestamp", "primaryKey": false, "notNull": false}, "actual_end_time": {"name": "actual_end_time", "type": "timestamp", "primaryKey": false, "notNull": false}, "estimated_cost": {"name": "estimated_cost", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "actual_cost": {"name": "actual_cost", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "labor_hours": {"name": "labor_hours", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "urgency_score": {"name": "urgency_score", "type": "integer", "primaryKey": false, "notNull": false, "default": 50}, "complexity_score": {"name": "complexity_score", "type": "integer", "primaryKey": false, "notNull": false, "default": 50}, "work_performed": {"name": "work_performed", "type": "text", "primaryKey": false, "notNull": false}, "parts_used": {"name": "parts_used", "type": "json", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "internal_notes": {"name": "internal_notes", "type": "text", "primaryKey": false, "notNull": false}, "customer_rating": {"name": "customer_rating", "type": "integer", "primaryKey": false, "notNull": false}, "customer_feedback": {"name": "customer_feedback", "type": "text", "primaryKey": false, "notNull": false}, "before_photos": {"name": "before_photos", "type": "json", "primaryKey": false, "notNull": false}, "after_photos": {"name": "after_photos", "type": "json", "primaryKey": false, "notNull": false}, "attachments": {"name": "attachments", "type": "json", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"service_tickets_company_id_companies_id_fk": {"name": "service_tickets_company_id_companies_id_fk", "tableFrom": "service_tickets", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "service_tickets_customer_id_customers_id_fk": {"name": "service_tickets_customer_id_customers_id_fk", "tableFrom": "service_tickets", "tableTo": "customers", "columnsFrom": ["customer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "service_tickets_building_id_buildings_id_fk": {"name": "service_tickets_building_id_buildings_id_fk", "tableFrom": "service_tickets", "tableTo": "buildings", "columnsFrom": ["building_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "service_tickets_assigned_technician_id_technicians_id_fk": {"name": "service_tickets_assigned_technician_id_technicians_id_fk", "tableFrom": "service_tickets", "tableTo": "technicians", "columnsFrom": ["assigned_technician_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.technicians": {"name": "technicians", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "integer", "primaryKey": false, "notNull": true}, "employee_id": {"name": "employee_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "technician_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'available'"}, "specializations": {"name": "specializations", "type": "json", "primaryKey": false, "notNull": false}, "certifications": {"name": "certifications", "type": "json", "primaryKey": false, "notNull": false}, "hourly_rate": {"name": "hourly_rate", "type": "numeric(8, 2)", "primaryKey": false, "notNull": false}, "current_latitude": {"name": "current_latitude", "type": "numeric(10, 8)", "primaryKey": false, "notNull": false}, "current_longitude": {"name": "current_longitude", "type": "numeric(11, 8)", "primaryKey": false, "notNull": false}, "last_location_update": {"name": "last_location_update", "type": "timestamp", "primaryKey": false, "notNull": false}, "average_rating": {"name": "average_rating", "type": "numeric(3, 2)", "primaryKey": false, "notNull": false, "default": "'5.0'"}, "completed_jobs": {"name": "completed_jobs", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "working_hours": {"name": "working_hours", "type": "json", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "hire_date": {"name": "hire_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"technicians_company_id_companies_id_fk": {"name": "technicians_company_id_companies_id_fk", "tableFrom": "technicians", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.transcriptions": {"name": "transcriptions", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "integer", "primaryKey": false, "notNull": true}, "customer_id": {"name": "customer_id", "type": "integer", "primaryKey": false, "notNull": false}, "service_ticket_id": {"name": "service_ticket_id", "type": "integer", "primaryKey": false, "notNull": false}, "audio_url": {"name": "audio_url", "type": "text", "primaryKey": false, "notNull": false}, "transcript": {"name": "transcript", "type": "text", "primaryKey": false, "notNull": true}, "context": {"name": "context", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "confidence": {"name": "confidence", "type": "numeric(5, 4)", "primaryKey": false, "notNull": false}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": false}, "language": {"name": "language", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false, "default": "'pl'"}, "analysis": {"name": "analysis", "type": "json", "primaryKey": false, "notNull": false}, "intent": {"name": "intent", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "sentiment_score": {"name": "sentiment_score", "type": "numeric(3, 2)", "primaryKey": false, "notNull": false}, "urgency_level": {"name": "urgency_level", "type": "service_ticket_priority", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'normal'"}, "is_processed": {"name": "is_processed", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "follow_up_required": {"name": "follow_up_required", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"transcriptions_company_id_companies_id_fk": {"name": "transcriptions_company_id_companies_id_fk", "tableFrom": "transcriptions", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "transcriptions_customer_id_customers_id_fk": {"name": "transcriptions_customer_id_customers_id_fk", "tableFrom": "transcriptions", "tableTo": "customers", "columnsFrom": ["customer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "transcriptions_service_ticket_id_service_tickets_id_fk": {"name": "transcriptions_service_ticket_id_service_tickets_id_fk", "tableFrom": "transcriptions", "tableTo": "service_tickets", "columnsFrom": ["service_ticket_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.communication_direction": {"name": "communication_direction", "schema": "public", "values": ["inbound", "outbound"]}, "public.communication_type": {"name": "communication_type", "schema": "public", "values": ["email", "phone", "sms", "in_person", "chat"]}, "public.customer_flow_state": {"name": "customer_flow_state", "schema": "public", "values": ["initial_contact", "quote_requested", "quote_sent", "quote_approved", "service_scheduled", "service_in_progress", "service_completed", "invoice_sent", "payment_received", "follow_up_scheduled", "maintenance_program"]}, "public.equipment_status": {"name": "equipment_status", "schema": "public", "values": ["active", "maintenance_required", "out_of_service", "retired"]}, "public.invoice_status": {"name": "invoice_status", "schema": "public", "values": ["draft", "sent", "paid", "overdue", "cancelled"]}, "public.service_ticket_priority": {"name": "service_ticket_priority", "schema": "public", "values": ["low", "normal", "high", "urgent", "emergency"]}, "public.service_ticket_status": {"name": "service_ticket_status", "schema": "public", "values": ["new", "assigned", "in_progress", "waiting_parts", "completed", "cancelled", "on_hold"]}, "public.technician_status": {"name": "technician_status", "schema": "public", "values": ["available", "busy", "on_route", "offline", "break"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}