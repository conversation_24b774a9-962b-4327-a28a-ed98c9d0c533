CREATE TYPE "public"."communication_direction" AS ENUM('inbound', 'outbound');--> statement-breakpoint
CREATE TYPE "public"."communication_type" AS ENUM('email', 'phone', 'sms', 'in_person', 'chat');--> statement-breakpoint
CREATE TYPE "public"."customer_flow_state" AS ENUM('initial_contact', 'quote_requested', 'quote_sent', 'quote_approved', 'service_scheduled', 'service_in_progress', 'service_completed', 'invoice_sent', 'payment_received', 'follow_up_scheduled', 'maintenance_program');--> statement-breakpoint
CREATE TYPE "public"."equipment_status" AS ENUM('active', 'maintenance_required', 'out_of_service', 'retired');--> statement-breakpoint
CREATE TYPE "public"."invoice_status" AS ENUM('draft', 'sent', 'paid', 'overdue', 'cancelled');--> statement-breakpoint
CREATE TYPE "public"."service_ticket_priority" AS ENUM('low', 'normal', 'high', 'urgent', 'emergency');--> statement-breakpoint
CREATE TYPE "public"."service_ticket_status" AS ENUM('new', 'assigned', 'in_progress', 'waiting_parts', 'completed', 'cancelled', 'on_hold');--> statement-breakpoint
CREATE TYPE "public"."technician_status" AS ENUM('available', 'busy', 'on_route', 'offline', 'break');--> statement-breakpoint
CREATE TABLE "buildings" (
	"id" serial PRIMARY KEY NOT NULL,
	"customer_id" integer NOT NULL,
	"name" varchar(255),
	"address" text NOT NULL,
	"city" varchar(100),
	"state" varchar(50),
	"zip_code" varchar(20),
	"building_type" varchar(100),
	"square_footage" integer,
	"floors" integer DEFAULT 1,
	"year_built" integer,
	"heating_type" varchar(100),
	"cooling_type" varchar(100),
	"latitude" numeric(10, 8),
	"longitude" numeric(11, 8),
	"notes" text,
	"is_active" boolean DEFAULT true,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "communications" (
	"id" serial PRIMARY KEY NOT NULL,
	"company_id" integer NOT NULL,
	"customer_id" integer,
	"service_ticket_id" integer,
	"type" "communication_type" NOT NULL,
	"direction" "communication_direction" NOT NULL,
	"subject" varchar(500),
	"content" text,
	"from_email" varchar(255),
	"to_email" varchar(255),
	"from_phone" varchar(50),
	"to_phone" varchar(50),
	"sentiment_score" numeric(3, 2),
	"category" varchar(100),
	"priority" "service_ticket_priority" DEFAULT 'normal',
	"intent" varchar(100),
	"auto_response_sent" boolean DEFAULT false,
	"requires_human_attention" boolean DEFAULT false,
	"is_processed" boolean DEFAULT false,
	"attachments" json,
	"transcription_id" integer,
	"thread_id" varchar(100),
	"parent_id" integer,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "companies" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"email" varchar(255),
	"phone" varchar(50),
	"address" text,
	"city" varchar(100),
	"state" varchar(50),
	"zip_code" varchar(20),
	"country" varchar(100) DEFAULT 'Poland',
	"website" varchar(255),
	"logo" text,
	"settings" json,
	"is_active" boolean DEFAULT true,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "customers" (
	"id" serial PRIMARY KEY NOT NULL,
	"company_id" integer NOT NULL,
	"first_name" varchar(100) NOT NULL,
	"last_name" varchar(100) NOT NULL,
	"email" varchar(255),
	"phone" varchar(50),
	"alternate_phone" varchar(50),
	"address" text,
	"city" varchar(100),
	"state" varchar(50),
	"zip_code" varchar(20),
	"country" varchar(100) DEFAULT 'Poland',
	"flow_state" "customer_flow_state" DEFAULT 'initial_contact',
	"preferred_contact" "communication_type" DEFAULT 'email',
	"customer_value" numeric(10, 2) DEFAULT '0',
	"last_service_date" timestamp,
	"next_maintenance_date" timestamp,
	"health_score" integer DEFAULT 100,
	"churn_risk" integer DEFAULT 0,
	"sentiment_score" numeric(3, 2) DEFAULT '0',
	"source" varchar(100),
	"notes" text,
	"tags" json,
	"is_active" boolean DEFAULT true,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "equipment" (
	"id" serial PRIMARY KEY NOT NULL,
	"building_id" integer NOT NULL,
	"name" varchar(255) NOT NULL,
	"manufacturer" varchar(100),
	"model" varchar(100),
	"serial_number" varchar(100),
	"equipment_type" varchar(100),
	"installation_date" timestamp,
	"warranty_expiration" timestamp,
	"last_maintenance_date" timestamp,
	"next_maintenance_date" timestamp,
	"status" "equipment_status" DEFAULT 'active',
	"health_score" integer DEFAULT 100,
	"specifications" json,
	"manual_url" text,
	"maintenance_history" json,
	"notes" text,
	"is_active" boolean DEFAULT true,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "invoices" (
	"id" serial PRIMARY KEY NOT NULL,
	"company_id" integer NOT NULL,
	"customer_id" integer NOT NULL,
	"service_ticket_id" integer,
	"invoice_number" varchar(50) NOT NULL,
	"status" "invoice_status" DEFAULT 'draft',
	"subtotal" numeric(10, 2) NOT NULL,
	"tax_amount" numeric(10, 2) DEFAULT '0',
	"discount_amount" numeric(10, 2) DEFAULT '0',
	"total_amount" numeric(10, 2) NOT NULL,
	"line_items" json,
	"issue_date" timestamp DEFAULT now() NOT NULL,
	"due_date" timestamp,
	"paid_date" timestamp,
	"payment_method" varchar(50),
	"payment_reference" varchar(100),
	"terms" text,
	"notes" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "parts_inventory" (
	"id" serial PRIMARY KEY NOT NULL,
	"company_id" integer NOT NULL,
	"part_number" varchar(100) NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"manufacturer" varchar(100),
	"category" varchar(100),
	"quantity_in_stock" integer DEFAULT 0,
	"minimum_stock" integer DEFAULT 0,
	"maximum_stock" integer,
	"reorder_point" integer DEFAULT 0,
	"cost" numeric(10, 2),
	"price" numeric(10, 2),
	"supplier_name" varchar(255),
	"supplier_part_number" varchar(100),
	"location" varchar(100),
	"weight" numeric(8, 3),
	"dimensions" varchar(100),
	"is_active" boolean DEFAULT true,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "service_tickets" (
	"id" serial PRIMARY KEY NOT NULL,
	"company_id" integer NOT NULL,
	"customer_id" integer NOT NULL,
	"building_id" integer,
	"assigned_technician_id" integer,
	"ticket_number" varchar(50) NOT NULL,
	"title" varchar(255) NOT NULL,
	"description" text,
	"status" "service_ticket_status" DEFAULT 'new',
	"priority" "service_ticket_priority" DEFAULT 'normal',
	"service_type" varchar(100),
	"equipment_type" varchar(100),
	"problem_category" varchar(100),
	"scheduled_date" timestamp,
	"estimated_duration" integer,
	"actual_start_time" timestamp,
	"actual_end_time" timestamp,
	"estimated_cost" numeric(10, 2),
	"actual_cost" numeric(10, 2),
	"labor_hours" numeric(5, 2),
	"urgency_score" integer DEFAULT 50,
	"complexity_score" integer DEFAULT 50,
	"work_performed" text,
	"parts_used" json,
	"notes" text,
	"internal_notes" text,
	"customer_rating" integer,
	"customer_feedback" text,
	"before_photos" json,
	"after_photos" json,
	"attachments" json,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "technicians" (
	"id" serial PRIMARY KEY NOT NULL,
	"company_id" integer NOT NULL,
	"employee_id" varchar(50),
	"first_name" varchar(100) NOT NULL,
	"last_name" varchar(100) NOT NULL,
	"email" varchar(255),
	"phone" varchar(50),
	"status" "technician_status" DEFAULT 'available',
	"specializations" json,
	"certifications" json,
	"hourly_rate" numeric(8, 2),
	"current_latitude" numeric(10, 8),
	"current_longitude" numeric(11, 8),
	"last_location_update" timestamp,
	"average_rating" numeric(3, 2) DEFAULT '5.0',
	"completed_jobs" integer DEFAULT 0,
	"working_hours" json,
	"is_active" boolean DEFAULT true,
	"hire_date" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "transcriptions" (
	"id" serial PRIMARY KEY NOT NULL,
	"company_id" integer NOT NULL,
	"customer_id" integer,
	"service_ticket_id" integer,
	"audio_url" text,
	"transcript" text NOT NULL,
	"context" varchar(50),
	"confidence" numeric(5, 4),
	"duration" integer,
	"language" varchar(10) DEFAULT 'pl',
	"analysis" json,
	"intent" varchar(100),
	"sentiment_score" numeric(3, 2),
	"urgency_level" "service_ticket_priority" DEFAULT 'normal',
	"is_processed" boolean DEFAULT false,
	"follow_up_required" boolean DEFAULT false,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "buildings" ADD CONSTRAINT "buildings_customer_id_customers_id_fk" FOREIGN KEY ("customer_id") REFERENCES "public"."customers"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "communications" ADD CONSTRAINT "communications_company_id_companies_id_fk" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "communications" ADD CONSTRAINT "communications_customer_id_customers_id_fk" FOREIGN KEY ("customer_id") REFERENCES "public"."customers"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "communications" ADD CONSTRAINT "communications_service_ticket_id_service_tickets_id_fk" FOREIGN KEY ("service_ticket_id") REFERENCES "public"."service_tickets"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "communications" ADD CONSTRAINT "communications_parent_id_communications_id_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."communications"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "customers" ADD CONSTRAINT "customers_company_id_companies_id_fk" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "equipment" ADD CONSTRAINT "equipment_building_id_buildings_id_fk" FOREIGN KEY ("building_id") REFERENCES "public"."buildings"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "invoices" ADD CONSTRAINT "invoices_company_id_companies_id_fk" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "invoices" ADD CONSTRAINT "invoices_customer_id_customers_id_fk" FOREIGN KEY ("customer_id") REFERENCES "public"."customers"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "invoices" ADD CONSTRAINT "invoices_service_ticket_id_service_tickets_id_fk" FOREIGN KEY ("service_ticket_id") REFERENCES "public"."service_tickets"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "parts_inventory" ADD CONSTRAINT "parts_inventory_company_id_companies_id_fk" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "service_tickets" ADD CONSTRAINT "service_tickets_company_id_companies_id_fk" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "service_tickets" ADD CONSTRAINT "service_tickets_customer_id_customers_id_fk" FOREIGN KEY ("customer_id") REFERENCES "public"."customers"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "service_tickets" ADD CONSTRAINT "service_tickets_building_id_buildings_id_fk" FOREIGN KEY ("building_id") REFERENCES "public"."buildings"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "service_tickets" ADD CONSTRAINT "service_tickets_assigned_technician_id_technicians_id_fk" FOREIGN KEY ("assigned_technician_id") REFERENCES "public"."technicians"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "technicians" ADD CONSTRAINT "technicians_company_id_companies_id_fk" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "transcriptions" ADD CONSTRAINT "transcriptions_company_id_companies_id_fk" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "transcriptions" ADD CONSTRAINT "transcriptions_customer_id_customers_id_fk" FOREIGN KEY ("customer_id") REFERENCES "public"."customers"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "transcriptions" ADD CONSTRAINT "transcriptions_service_ticket_id_service_tickets_id_fk" FOREIGN KEY ("service_ticket_id") REFERENCES "public"."service_tickets"("id") ON DELETE no action ON UPDATE no action;