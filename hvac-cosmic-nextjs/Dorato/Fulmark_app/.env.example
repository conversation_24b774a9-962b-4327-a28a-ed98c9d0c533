# 🌟 HVAC CRM Environment Variables - PEŁNA MOC WIATRU! ⚡

# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/hvac_crm"

# OpenAI Configuration (for AI-powered features)
OPENAI_API_KEY="sk-your-openai-api-key-here"

# Twilio Configuration (for SMS integration)
TWILIO_ACCOUNT_SID="your-twilio-account-sid"
TWILIO_AUTH_TOKEN="your-twilio-auth-token"
TWILIO_PHONE_NUMBER="+**********"

# Resend Configuration (for email sending)
RESEND_API_KEY="re_your-resend-api-key-here"

# Supabase Configuration (for file storage and real-time features)
SUPABASE_URL="https://your-project.supabase.co"
SUPABASE_ANON_KEY="your-supabase-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-supabase-service-role-key"

# Company Configuration
COMPANY_NAME="Fulmark HVAC Services"
COMPANY_PHONE="+48 ***********"
COMPANY_EMAIL="<EMAIL>"
EMERGENCY_PHONE="+48 ***********"

# Next.js Configuration
NEXTAUTH_SECRET="your-nextauth-secret-key"
NEXTAUTH_URL="http://localhost:3000"

# Environment
NODE_ENV="development"

# Optional: External API Keys
MAPBOX_ACCESS_TOKEN="your-mapbox-token-for-maps"
STRIPE_SECRET_KEY="sk_test_your-stripe-secret-key"
STRIPE_PUBLISHABLE_KEY="pk_test_your-stripe-publishable-key"

# Optional: Analytics and Monitoring
SENTRY_DSN="your-sentry-dsn"
POSTHOG_KEY="your-posthog-key"

# Optional: File Upload Configuration
MAX_FILE_SIZE="10485760" # 10MB in bytes
ALLOWED_FILE_TYPES="image/jpeg,image/png,image/webp,application/pdf"