import axios from 'axios';

const API_URL = '/api/v1/auth';

interface LoginResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
}

export const authService = {
  async login(email: string, password: string): Promise<LoginResponse> {
    const formData = new URLSearchParams();
    formData.append('username', email);
    formData.append('password', password);
    
    const response = await axios.post<LoginResponse>(
      `${API_URL}/token`,
      formData,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      }
    );
    
    return response.data;
  },

  async register(email: string, password: string, name: string): Promise<void> {
    await axios.post(`${API_URL}/register`, {
      email,
      password,
      name,
    });
  },

  async refreshToken(): Promise<LoginResponse> {
    const response = await axios.post<LoginResponse>(
      `${API_URL}/refresh`,
      {},
      {
        withCredentials: true,
      }
    );
    
    return response.data;
  },

  async logout(): Promise<void> {
    await axios.post(`${API_URL}/logout`);
  },

  async getCurrentUser(): Promise<any> {
    const response = await axios.get(`${API_URL}/me`);
    return response.data;
  },
};

export default authService;
