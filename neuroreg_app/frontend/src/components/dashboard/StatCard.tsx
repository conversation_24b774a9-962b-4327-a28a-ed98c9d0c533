import { Box, Typography, Paper, SvgIcon, useTheme } from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Equal as EqualIcon,
} from '@mui/icons-material';

// Define the icon components for different stat types
const StatIcons = {
  sessions: (
    <SvgIcon>
      <path d="M13 3c-4.97 0-9 4.03-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42C8.27 19.99 10.51 21 13 21c4.97 0 9-4.03 9-9s-4.03-9-9-9zm-1 5v5l4.28 2.54.72-1.21-3.5-2.08V8H12z" />
    </SvgIcon>
  ),
  hrv: (
    <SvgIcon>
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm4.59-12.42L10 14.17l-2.59-2.58L6 13l4 4 8-8z" />
    </SvgIcon>
  ),
  stress: (
    <SvgIcon>
      <path d="M12 3c-4.97 0-9 4.03-9 9s4.03 9 9 9 9-4.03 9-9-4.03-9-9-9zm0 16c-3.86 0-7-3.14-7-7s3.14-7 7-7 7 3.14 7 7-3.14 7-7 7zm1-11h-2v6h2V8zm0 8h-2v2h2v-2z" />
    </SvgIcon>
  ),
  recovery: (
    <SvgIcon>
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z" />
    </SvgIcon>
  ),
};

interface StatCardProps {
  title: string;
  value: string | number;
  change?: number;
  icon: keyof typeof StatIcons;
  isNegative?: boolean;
}

const StatCard = ({
  title,
  value,
  change = 0,
  icon,
  isNegative = false,
}: StatCardProps) => {
  const theme = useTheme();
  
  const getChangeColor = () => {
    if (change > 0) return theme.palette.success.main;
    if (change < 0) return theme.palette.error.main;
    return theme.palette.text.secondary;
  };

  const getChangeIcon = () => {
    if (change > 0) return <TrendingUpIcon fontSize="small" />;
    if (change < 0) return <TrendingDownIcon fontSize="small" />;
    return <EqualIcon fontSize="small" />;
  };

  return (
    <Paper
      elevation={0}
      sx={{
        p: 3,
        height: '100%',
        borderRadius: 2,
        border: `1px solid ${theme.palette.divider}`,
        transition: 'all 0.3s ease',
        '&:hover': {
          boxShadow: theme.shadows[4],
          transform: 'translateY(-4px)',
        },
      }}
    >
      <Box display="flex" justifyContent="space-between" alignItems="center">
        <Box>
          <Typography
            variant="subtitle2"
            color="text.secondary"
            gutterBottom
          >
            {title}
          </Typography>
          <Typography variant="h4" component="div">
            {value}
          </Typography>
          {change !== undefined && (
            <Box display="flex" alignItems="center" mt={1}>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  color: getChangeColor(),
                }}
              >
                {getChangeIcon()}
                <Typography
                  variant="body2"
                  sx={{ ml: 0.5, fontWeight: 500 }}
                >
                  {Math.abs(change)}%
                </Typography>
              </Box>
              <Typography
                variant="caption"
                color="text.secondary"
                sx={{ ml: 1 }}
              >
                vs last period
              </Typography>
            </Box>
          )}
        </Box>
        <Box
          sx={{
            p: 1.5,
            borderRadius: '50%',
            bgcolor: isNegative
              ? 'error.light'
              : theme.palette.mode === 'light'
              ? 'primary.light'
              : 'primary.dark',
            color: isNegative ? 'error.contrastText' : 'primary.contrastText',
          }}
        >
          {StatIcons[icon]}
        </Box>
      </Box>
    </Paper>
  );
};

export default StatCard;
