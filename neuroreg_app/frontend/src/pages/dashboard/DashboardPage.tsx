import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  Box,
  Grid,
  Paper,
  Typography,
  CircularProgress,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
} from 'recharts';
import { dashboardService } from '@/services/dashboardService';
import StatCard from '@/components/dashboard/StatCard';
import { formatDate } from '@/utils/dateUtils';

// Sample data for the dashboard (replace with real data from your API)
const sampleData = {
  stats: {
    totalSessions: 124,
    avgHrv: 65.2,
    avgStress: 42.8,
    avgRecovery: 78.5,
  },
  hrvTrend: [
    { date: '2023-01-01', hrv: 62 },
    { date: '2023-01-02', hrv: 64 },
    { date: '2023-01-03', hrv: 63 },
    { date: '2023-01-04', hrv: 66 },
    { date: '2023-01-05', hrv: 67 },
    { date: '2023-01-06', hrv: 65 },
    { date: '2023-01-07', hrv: 68 },
  ],
  stressByTime: [
    { time: '00:00', stress: 45 },
    { time: '04:00', stress: 40 },
    { time: '08:00', stress: 55 },
    { time: '12:00', stress: 60 },
    { time: '16:00', stress: 50 },
    { time: '20:00', stress: 42 },
  ],
  recoveryDistribution: [
    { name: 'Optimal', value: 65 },
    { name: 'Good', value: 20 },
    { name: 'Fair', value: 10 },
    { name: 'Poor', value: 5 },
  ],
};

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];

const DashboardPage = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [timeRange, setTimeRange] = useState('7d');

  // Fetch dashboard data
  const { data, isLoading, error } = useQuery(['dashboard', timeRange], () =>
    dashboardService.getDashboardData(timeRange)
  );

  // Use sample data if API is not available
  const dashboardData = data || sampleData;
  const { stats, hrvTrend, stressByTime, recoveryDistribution } = dashboardData;

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
        <CircularProgress />
      </Box>
    );
  }


  return (
    <Box sx={{ flexGrow: 1 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Dashboard
      </Typography>
      
      {/* Stats Overview */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Sessions"
            value={stats.totalSessions}
            change={12}
            icon="sessions"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Avg HRV"
            value={`${stats.avgHrv} ms`}
            change={2.5}
            icon="hrv"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Avg Stress"
            value={`${stats.avgStress}%`}
            change={-5.2}
            icon="stress"
            isNegative={true}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Recovery Score"
            value={`${stats.avgRecovery}%`}
            change={3.8}
            icon="recovery"
          />
        </Grid>
      </Grid>

      {/* Charts Row 1 */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 2, height: 400 }}>
            <Typography variant="h6" gutterBottom>
              HRV Trend
            </Typography>
            <ResponsiveContainer width="100%" height="90%">
              <LineChart data={hrvTrend}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="hrv"
                  name="HRV (ms)"
                  stroke="#8884d8"
                  activeDot={{ r: 8 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2, height: 400 }}>
            <Typography variant="h6" gutterBottom>
              Recovery Distribution
            </Typography>
            <ResponsiveContainer width="100%" height="90%">
              <PieChart>
                <Pie
                  data={recoveryDistribution}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={120}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) =>
                    `${name}: ${(percent * 100).toFixed(0)}%`
                  }
                >
                  {recoveryDistribution.map((entry, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={COLORS[index % COLORS.length]}
                    />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => [`${value}%`, 'Percentage']} />
              </PieChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
      </Grid>

      {/* Charts Row 2 */}
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Paper sx={{ p: 2, height: 400 }}>
            <Typography variant="h6" gutterBottom>
              Stress Levels by Time of Day
            </Typography>
            <ResponsiveContainer width="100%" height="90%">
              <BarChart data={stressByTime}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="time" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="stress" name="Stress Level" fill="#82ca9d" />
              </BarChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default DashboardPage;
